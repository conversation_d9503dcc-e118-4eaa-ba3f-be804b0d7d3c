import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    componentProps: {
      placeholder: '请输入用户ID',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '用户编号',
    componentProps: {
      placeholder: '请输入用户编号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'meterNo',
    label: '水表编号',
    componentProps: {
      placeholder: '请输入水表编号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'billPeriod',
    label: '账期',
    componentProps: {
      placeholder: '请输入账期',
      allowClear: true,
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'originalAmount',
    label: '原应收金额',
    componentProps: {
      placeholder: '请输入原应收金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'adjustedAmount',
    label: '调整后金额',
    componentProps: {
      placeholder: '请输入调整后金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    component: 'Input',
    fieldName: 'reason',
    label: '调整原因',
    componentProps: {
      placeholder: '请输入调整原因',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'adjustmentType',
    label: '调整类型',
    componentProps: {
      placeholder: '请选择调整类型',
      options: [
        { label: '水费调整', value: 'WATER_FEE' },
        { label: '违约金调整', value: 'PENALTY' },
        { label: '附加费调整', value: 'ADDITIONAL_FEE' },
        { label: '其他调整', value: 'OTHER' },
      ],
      allowClear: true,
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键',
    field: 'id',
  },
  {
    title: '用户ID',
    field: 'userId',
  },
  {
    title: '水表编号',
    field: 'meterNo',
  },
  {
    title: '账期',
    field: 'billPeriod',
  },
  {
    title: '原应收金额',
    field: 'originalAmount',
  },
  {
    title: '调整后金额',
    field: 'adjustedAmount',
  },
  {
    title: '调整原因',
    field: 'reason',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    rules: 'required',
    componentProps: {
      placeholder: '请输入用户ID',
    },
  },
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '用户编号',
    componentProps: {
      placeholder: '请输入用户编号',
    },
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用户姓名',
    componentProps: {
      placeholder: '请输入用户姓名',
    },
  },
  {
    component: 'Input',
    fieldName: 'meterNo',
    label: '水表编号',
    rules: 'required',
    componentProps: {
      placeholder: '请输入水表编号',
    },
  },
  {
    component: 'Input',
    fieldName: 'billPeriod',
    label: '账期',
    rules: 'required',
    componentProps: {
      placeholder: '请输入账期，如：2024-01',
    },
  },
  {
    component: 'Input',
    fieldName: 'billNumber',
    label: '账单编号',
    componentProps: {
      placeholder: '请输入账单编号',
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'originalAmount',
    label: '原应收金额',
    rules: 'required',
    componentProps: {
      placeholder: '请输入原应收金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
      addonAfter: '元',
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'adjustedAmount',
    label: '调整后金额',
    rules: 'required',
    componentProps: {
      placeholder: '请输入调整后金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
      addonAfter: '元',
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'adjustmentAmount',
    label: '调整金额',
    componentProps: {
      placeholder: '调整金额（自动计算）',
      precision: 2,
      style: { width: '100%' },
      addonAfter: '元',
      disabled: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'adjustmentType',
    label: '调整类型',
    rules: 'required',
    componentProps: {
      placeholder: '请选择调整类型',
      options: [
        { label: '水费调整', value: 'WATER_FEE' },
        { label: '违约金调整', value: 'PENALTY' },
        { label: '附加费调整', value: 'ADDITIONAL_FEE' },
        { label: '其他调整', value: 'OTHER' },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'reason',
    label: '调整原因',
    rules: 'required',
    componentProps: {
      placeholder: '请输入调整原因',
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'adjustmentTime',
    label: '调整时间',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择调整时间',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    component: 'Input',
    fieldName: 'operator',
    label: '操作员',
    componentProps: {
      placeholder: '请输入操作员',
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
];
