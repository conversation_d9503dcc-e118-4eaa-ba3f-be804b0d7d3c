import type { ID } from '#/api/common';

// 公共信息模型
export interface PublicInfoModel {
  /** 信息ID */
  infoId?: ID;
  /** 标题 */
  title?: string;
  /** 内容 */
  content?: string;
  /** 发布时间 */
  publishTime?: string;
  /** 信息类型：1-停水通知，2-供水公告，3-业务常识，4-水费标准，5-营商环境，6-公司简介 */
  infoType?: number;
  /** 排序 */
  sort?: number;
  /** 状态：0-禁用，1-启用 */
  status?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 附件链接 */
  attachmentUrl?: string;
  /** 发布人 */
  publisher?: string;
  /** 附件名称 */
  attachmentName?: string;
}

// 公共信息查询参数
export interface PublicInfoParams {
  /** 关键字 */
  keyword?: string;
  /** 信息类型：1-停水通知，2-供水公告，3-业务常识，4-水费标准，5-营商环境，6-公司简介 */
  infoType?: number;
  /** 发布时间开始 */
  publishTimeBegin?: string;
  /** 发布时间结束 */
  publishTimeEnd?: string;
  /** 状态：0-禁用，1-启用 */
  status?: number;
  /** 当前页 */
  current?: number;
  /** 每页条数 */
  size?: number;
}
