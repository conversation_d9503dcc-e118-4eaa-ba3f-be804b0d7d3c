import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  // {
  //   component: 'Input',
  //   fieldName: 'userId',
  //   label: '用户ID',
  // },
  // {
  //   component: 'Input',
  //   fieldName: 'userNo',
  //   label: '用户编号',
  // },
  {
    component: 'Input',
    fieldName: 'searchValue',
    label: '关键字',
    componentProps: {
      placeholder: '请输入户号或名称',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_user_customer_nature'),
    },
    fieldName: 'customerNature',
    label: '客户性质',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'useWaterNature',
    label: '用水性质',
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用水户名称',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_user_user_status'),
    },
    fieldName: 'userStatus',
    label: '用户状态',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('yes_no'),
    },
    fieldName: 'ifSpecific',
    label: '重点户',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: '用户ID',
  //   field: 'userId',
  // },
  {
    title: '户号',
    field: 'userNo',
  },
  {
    title: '用户名称',
    field: 'userName',
  },
  {
    title: '用水性质',
    field: 'useWaterNature',
    slots: {
      default: ({ row }) => {
        return renderDict(row.useWaterNature, 'waterfee_user_use_water_nature');
      },
    },
  },
  {
    title: '客户性质',
    field: 'customerNature',
    slots: {
      default: ({ row }) => {
        return renderDict(row.customerNature, 'waterfee_user_customer_nature');
      },
    },
  },
  {
    title: '抄表手册',
    field: 'bookName',
  },
  {
    title: '水表编号',
    field: 'meterNo',
  },
  {
    title: '用户状态',
    field: 'userStatus',
    slots: {
      default: ({ row }) => {
        return renderDict(row.userStatus, 'waterfee_user_user_status');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 380,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'userId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '户号',
  },
  {
    component: 'Input',
    fieldName: 'areaId',
    label: '营业区域id',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'communityId',
    label: '小区id',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'unitRoomNumber',
    label: '单元房号',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_customer_nature'),
    },
    fieldName: 'customerNature',
    label: '客户性质',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'useWaterNature',
    label: '用水性质',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'useWaterNumber',
    label: '用水人数',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用水户名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'phoneNumber',
    label: '手机号码',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_certificate_type'),
    },
    fieldName: 'certificateType',
    label: '证件类型',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'certificateNumber',
    label: '证件号码',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_user_status'),
    },
    fieldName: 'userStatus',
    label: '用户状态',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'address',
    label: '用水地址',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'email',
    label: '电子邮箱',
  },
  {
    component: 'Input',
    fieldName: 'taxpayerIdentificationNumber',
    label: '纳税人识别号',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'supplyDate',
    label: '供水日期',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'invoiceName',
    label: '开票名称',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_invoice_type'),
    },
    fieldName: 'invoiceType',
    label: '发票类型',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'meterNo',
    label: '水表编号',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'priceUseWaterNature',
    label: '价格-用水性质',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_billing_method'),
    },
    fieldName: 'billingMethod',
    label: '计费方式',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('yes_no'),
      optionType: 'button',
    },
    fieldName: 'ifPenalty',
    label: '是否有违约金',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_penalty_type'),
    },
    fieldName: 'penaltyType',
    label: '违约金类型',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('yes_no'),
      optionType: 'button',
    },
    fieldName: 'ifExtraCharge',
    label: '是否有附加费',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_extra_charge_type'),
    },
    fieldName: 'extraChargeType',
    label: '附加费内容',
  },
];
