<div align="center"> <a href="https://github.com/anncwb/vue-vben-admin"> <img alt="VbenAdmin Logo" width="215" src="https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp"> </a> <br> <br>

[![license](https://img.shields.io/github/license/anncwb/vue-vben-admin.svg)](LICENSE)

<h1>Vue Vben Admin</h1>
</div>

[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=vbenjs_vue-vben-admin&metric=alert_status)](https://sonarcloud.io/summary/new_code?id=vbenjs_vue-vben-admin) ![codeql](https://github.com/vbenjs/vue-vben-admin/actions/workflows/codeql.yml/badge.svg) ![build](https://github.com/vbenjs/vue-vben-admin/actions/workflows/build.yml/badge.svg) ![ci](https://github.com/vbenjs/vue-vben-admin/actions/workflows/ci.yml/badge.svg) ![deploy](https://github.com/vbenjs/vue-vben-admin/actions/workflows/deploy.yml/badge.svg)

**English** | [中文](./README.zh-CN.md) | [日本語](./README.ja-JP.md)

## Introduction

Vue Vben Admin is a free and open source middle and back-end template. Using the latest `vue3`, `vite`, `TypeScript` and other mainstream technology development, the out-of-the-box middle and back-end front-end solutions can also be used for learning reference.

## Upgrade Notice

This is the latest version, 5.0, and it is not compatible with previous versions. If you are starting a new project, it is recommended to use the latest version. If you wish to view the old version, please use the [v2 branch](https://github.com/vbenjs/vue-vben-admin/tree/v2).

## Feature

- **Latest Technology Stack**: Developed with cutting-edge front-end technologies like Vue 3 and Vite
- **TypeScript**: A language for application-scale JavaScript
- **Themes**: Multiple theme colors available with customizable options
- **Internationalization**: Comprehensive built-in internationalization support
- **Permissions**: Built-in solution for dynamic route-based permission generation

## Preview

- [Vben Admin](https://vben.pro/) - Full version Chinese site

Test Account: vben/123456

<p align="center">
    <img alt="VbenAdmin Logo" width="100%" src="https://anncwb.github.io/anncwb/images/preview1.png">
    <img alt="VbenAdmin Logo" width="100%" src="https://anncwb.github.io/anncwb/images/preview2.png">
    <img alt="VbenAdmin Logo" width="100%" src="https://anncwb.github.io/anncwb/images/preview3.png">
</p>

### Use Gitpod

Open the project in Gitpod (free online dev environment for GitHub) and start coding immediately.

[![Open in Gitpod](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/#https://github.com/vbenjs/vue-vben-admin)

## Documentation

[Document](https://doc.vben.pro/)

## Install and use

- Get the project code

```bash
git clone https://github.com/vbenjs/vue-vben-admin.git
```

- Installation dependencies

```bash
cd vue-vben-admin

corepack enable

pnpm install
```

- run

```bash
pnpm dev
```

- build

```bash
pnpm build
```

## Change Log

[CHANGELOG](https://github.com/vbenjs/vue-vben-admin/releases)

## How to contribute

You are very welcome to join！[Raise an issue](https://github.com/anncwb/vue-vben-admin/issues/new/choose) Or submit a Pull Request。

**Pull Request:**

1. Fork code!
2. Create your own branch: `git checkout -b feat/xxxx`
3. Submit your changes: `git commit -am 'feat(function): add xxxxx'`
4. Push your branch: `git push origin feat/xxxx`
5. submit`pull request`

## Git Contribution submission specification

- reference [vue](https://github.com/vuejs/vue/blob/dev/.github/COMMIT_CONVENTION.md) specification ([Angular](https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-angular))

  - `feat` Add new features
  - `fix` Fix the problem/BUG
  - `style` The code style is related and does not affect the running result
  - `perf` Optimization/performance improvement
  - `refactor` Refactor
  - `revert` Undo edit
  - `test` Test related
  - `docs` Documentation/notes
  - `chore` Dependency update/scaffolding configuration modification etc.
  - `ci` Continuous integration
  - `types` Type definition file changes
  - `wip` In development

## Browser support

The `Chrome 80+` browser is recommended for local development

Support modern browsers, not IE

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>IE | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari |
| :-: | :-: | :-: | :-: | :-: |
| not support | last 2 versions | last 2 versions | last 2 versions | last 2 versions |

## Maintainer

[@Vben](https://github.com/anncwb)

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=vbenjs/vue-vben-admin&type=Date)](https://star-history.com/#vbenjs/vue-vben-admin&Date)

## Donate

If you think this project is helpful to you, you can help the author buy a cup of coffee to show your support!

![donate](https://unpkg.com/@vbenjs/static-source@0.1.7/source/sponsor.png)

<a style="display: block;width: 100px;height: 50px;line-height: 50px; color: #fff;text-align: center; background: #408aee;border-radius: 4px;" href="https://www.paypal.com/paypalme/cvvben">Paypal Me</a>

## Contributor

<a href="https://github.com/vbenjs/vue-vben-admin/graphs/contributors">
  <img alt="Contributors"
        src="https://opencollective.com/vbenjs/contributors.svg?button=false" />
</a>

## Discord

- [Github Discussions](https://github.com/anncwb/vue-vben-admin/discussions)

## License

[MIT © Vben-2020](./LICENSE)

## 项目结构

```
./
├── .browserslistrc：浏览器列表配置文件
├── .commitlintrc.js：Commit 规范配置文件
├── .dockerignore：Docker 忽略文件
├── .editorconfig：Editor 配置文件
├── .gitattributes：Git 属性文件
├── .gitconfig：Git 配置文件
├── .gitignore：Git 忽略文件
├── .gitpod.yml：Gitpod 配置文件
├── .lintstagedrc.mjs：Lint Staged 配置文件
├── .node-version：Node 版本文件
├── .npmrc：NPM 配置文件
├── .prettierignore：Prettier 忽略文件
├── .prettierrc.mjs：Prettier 配置文件
├── .stylelintignore：Stylelint 忽略文件
├── CHANGELOG.md：更新日志
├── cspell.json：CSpell 配置文件
├── eslint.config.mjs：ESLint 配置文件
├── LICENSE：许可证
├── package.json：包管理配置文件
├── pnpm-workspace.yaml：PNPM 工作空间配置文件
├── README.ja-JP.md：日语 README
├── README.md：英文 README
├── README.zh-CN.md：中文 README
├── stylelint.config.mjs：Stylelint 配置文件
├── tea.yaml：Tea 配置文件
├── turbo.json：Turbo 配置文件
├── vben-admin.code-workspace：VSCode 工作空间配置文件
├── vitest.config.ts：Vitest 配置文件
├── vitest.workspace.ts：Vitest 工作空间配置文件
├── apps/：应用目录
│   ├── backend-mock/：后端 Mock 应用
│   │   ├── .env：环境变量文件
│   │   ├── error.ts：错误处理文件
│   │   ├── nitro.config.ts：Nitro 配置文件
│   │   ├── package.json：包管理配置文件
│   │   ├── README.md：后端 Mock 应用说明
│   │   ├── tsconfig.build.json：TypeScript 构建配置文件
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   ├── api/：API 接口目录
│   │   ├── middleware/：中间件目录
│   │   ├── routes/：路由目录
│   │   └── utils/：工具函数目录
│   └── web-antd/：前端 Web 应用
│       ├── .env：环境变量文件
│       ├── .env.analyze：分析环境变量文件
│       ├── .env.development：开发环境变量文件
│       ├── .env.production：生产环境变量文件
│       ├── .env.test：测试环境变量文件
│       ├── index.html：HTML 入口文件
│       ├── package.json：包管理配置文件
│       ├── postcss.config.mjs：PostCSS 配置文件
│       ├── tailwind.config.mjs：Tailwind CSS 配置文件
│       ├── tsconfig.json：TypeScript 配置文件
│       ├── tsconfig.node.json：TypeScript Node 配置文件
│       ├── vite.config.mts：Vite 配置文件
│       ├── public/：公共资源目录
│       └── src/：源代码目录
│       └── types/：类型定义目录
├── docs/：文档目录
│   ├── package.json：包管理配置文件
│   ├── tailwind.config.mjs：Tailwind CSS 配置文件
│   ├── tsconfig.json：TypeScript 配置文件
│   ├── src/：文档源代码目录
│   │   ├── index.md：文档首页
│   │   ├── _env/：环境变量目录
│   │   ├── commercial/：商业相关目录
│   │   ├── components/：组件目录
│   │   ├── demos/：示例目录
│   │   ├── en/：英文文档目录
│   │   ├── friend-links/：友情链接目录
│   │   ├── guide/：指南目录
│   │   ├── public/：公共资源目录
│   │   └── sponsor/：赞助目录
├── internal/：内部工具目录
│   ├── lint-configs/：Lint 配置文件目录
│   │   ├── commitlint-config/：Commitlint 配置文件目录
│   │   ├── eslint-config/：ESLint 配置文件目录
│   │   ├── prettier-config/：Prettier 配置文件目录
│   │   └── stylelint-config/：Stylelint 配置文件目录
│   ├── node-utils/：Node 工具函数目录
│   │   ├── build.config.ts：构建配置文件
│   │   ├── package.json：包管理配置文件
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   ├── tailwind-config/：Tailwind CSS 配置文件目录
│   │   ├── build.config.ts：构建配置文件
│   │   ├── package.json：包管理配置文件
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   └── tsconfig/：TypeScript 配置文件目录
│       ├── base.json：基础配置文件
│       ├── library.json：库配置文件
│       ├── node.json：Node 配置文件
│       ├── package.json：包配置文件
│       ├── web-app.json：Web 应用配置文件
│       └── web.json：Web 配置文件
│   └── vite-config/：Vite 配置文件目录
│       ├── build.config.ts：构建配置文件
│       ├── package.json：包管理配置文件
│       ├── tsconfig.json：TypeScript 配置文件
│       └── src/：源代码目录
├── packages/：包目录
│   ├── @core/：核心包
│   │   ├── README.md：核心包说明
│   │   ├── base/：基础功能目录
│   │   ├── composables/：组合式函数目录
│   │   ├── preferences/：偏好设置目录
│   │   └── ui-kit/：UI 组件目录
│   ├── constants/：常量包
│   │   ├── package.json：包管理配置文件
│   │   ├── README.md：常量包说明
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   ├── effects/：特效包
│   │   ├── README.md：特效包说明
│   │   ├── access/：权限相关目录
│   │   ├── common-ui/：通用 UI 目录
│   │   ├── hooks/：Hooks 目录
│   │   ├── layouts/：布局目录
│   │   ├── plugins/：插件目录
│   │   └── request/：请求目录
│   ├── icons/：图标包
│   │   ├── package.json：包管理配置文件
│   │   ├── README.md：图标包说明
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   ├── locales/：本地化包
│   │   ├── package.json：包管理配置文件
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   ├── preferences/：偏好设置包
│   │   ├── package.json：包管理配置文件
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   ├── stores/：状态管理包
│   │   ├── package.json：包管理配置文件
│   │   ├── shim-pinia.d.ts：Pinia 类型声明文件
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   ├── styles/：样式包
│   │   ├── package.json：包管理配置文件
│   │   ├── README.md：样式包说明
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   ├── types/：类型定义包
│   │   ├── global.d.ts：全局类型定义文件
│   │   ├── package.json：包管理配置文件
│   │   ├── README.md：类型定义包说明
│   │   ├── tsconfig.json：TypeScript 配置文件
│   │   └── src/：源代码目录
│   └── utils/：工具函数包
│       ├── package.json：包管理配置文件
│       ├── README.md：工具函数包说明
│       ├── tsconfig.json：TypeScript 配置文件
│       └── src/：源代码目录
├── playground/：示例项目
│   ├── .env：环境变量文件
│   ├── .env.analyze：分析环境变量文件
│   ├── .env.development：开发环境变量文件
│   ├── .env.production：生产环境变量文件
│   ├── index.html：HTML 入口文件
│   ├── package.json：包管理配置文件
│   ├── playwright.config.ts：Playwright 配置文件
│   ├── postcss.config.mjs：PostCSS 配置文件
│   ├── tailwind.config.mjs：Tailwind CSS 配置文件
│   ├── tsconfig.json：TypeScript 配置文件
│   ├── tsconfig.node.json：TypeScript Node 配置文件
│   ├── vite.config.mts：Vite 配置文件
│   ├── __tests__/：测试目录
│   │   └── e2e/：E2E 测试目录
│   ├── public/：公共资源目录
│   │   └── favicon.ico：网站图标
│   └── src/：源代码目录
│       ├── app.vue：根组件
│       ├── bootstrap.ts：启动文件
│       ├── main.ts：主文件
│       ├── preferences.ts：偏好设置文件
│       ├── adapter/：适配器目录
│       ├── api/：API 接口目录
│       ├── layouts/：布局目录
│       ├── locales/：本地化目录
│       ├── router/：路由目录
│       ├── store/：状态管理目录
│       └── views/：视图目录
├── scripts/：脚本目录
│   ├── clean.mjs：清理脚本
│   ├── 菜单图标替换sql/：菜单图标替换 SQL 脚本目录
│   ├── deploy/：部署脚本目录
│   │   ├── build-local-docker-image.sh：构建本地 Docker 镜像脚本
│   │   ├── Dockerfile：Dockerfile 文件
│   │   └── nginx.conf：Nginx 配置文件
│   ├── preview/：预览图片目录
│   │   ├── 1.png：预览图片 1
│   │   ├── 2.png：预览图片 2
│   │   ├── 3.png：预览图片 3
│   │   ├── 4.png：预览图片 4
│   │   ├── 5.png：预览图片 5
│   │   └── 6.png：预览图片 6
│   ├── turbo-run/：Turbo 运行脚本目录
│   └── vsh/：VSH 脚本目录
```
