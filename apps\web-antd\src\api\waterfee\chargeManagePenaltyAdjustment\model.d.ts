export interface ChargeManagePenaltyAdjustmentVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 
   */
  userId: string | number;

  /**
   * 
   */
  billId: string | number;

  /**
   * 原违约金
   */
  originalPenalty: number;

  /**
   * 调整后违约金
   */
  adjustedPenalty: number;

  /**
   * 
   */
  reason: string;

  /**
   * 
   */
  remark: string;

}

export interface ChargeManagePenaltyAdjustmentForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 
   */
  userId?: string | number;

  /**
   * 
   */
  billId?: string | number;

  /**
   * 原违约金
   */
  originalPenalty?: number;

  /**
   * 调整后违约金
   */
  adjustedPenalty?: number;

  /**
   * 
   */
  reason?: string;

  /**
   * 
   */
  remark?: string;

}

export interface ChargeManagePenaltyAdjustmentQuery extends PageQuery {

  /**
   * 
   */
  userId?: string | number;

  /**
   * 
   */
  billId?: string | number;

  /**
   * 原违约金
   */
  originalPenalty?: number;

  /**
   * 调整后违约金
   */
  adjustedPenalty?: number;

  /**
   * 
   */
  reason?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



