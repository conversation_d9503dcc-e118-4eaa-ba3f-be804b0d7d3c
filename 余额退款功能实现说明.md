# 余额退款功能实现说明

## 功能概述
在窗口收费系统中新增了余额退款功能，支持多种退款方式，包括现金、银行卡、微信、支付宝等，并提供完整的退款流程管理。

## 实现内容

### 1. API接口层面
**文件**: `apps/web-antd/src/api/waterfee/counter-payment.ts`

- **专用退款接口**: 新增 `/waterfee/counter-payment/deposit-refund` 接口
- **独立处理逻辑**: 退款使用专门的 `refundDeposit` 函数
- **完整退款参数**: 支持银行卡退款等复杂场景
- 支持参数：
  - `userId`: 用户ID
  - `refundAmount`: 退款金额（正数）
  - `refundReason`: 退款原因（必填）
  - `refundMethod`: 退款方式
  - `bankCardNo`: 银行卡号（银行卡退款时必填）
  - `bankName`: 开户行（银行卡退款时必填）
  - `accountName`: 账户名（银行卡退款时必填）
  - `remark`: 备注信息
  - `forceRefund`: 是否强制退款

### 2. 前端界面层面
**文件**: `apps/web-antd/src/views/waterfee/bill/counter-payment/index.vue`

#### 新增组件和数据
- 添加 `refundForm` 响应式表单对象
- 包含完整的退款字段：退款金额、退款原因、退款方式、银行卡信息等
- 支持动态显示银行卡相关字段

#### 新增标签页
- 在预存充值标签页后新增"余额退款"标签页
- 显示当前预存余额
- 提供完整的退款表单界面

#### 表单验证
- 退款金额必须大于0且不能超过当前余额
- 退款原因必填
- 退款方式必选
- 银行卡退款时，银行卡号、开户行、账户名必填
- 实时验证并禁用/启用提交按钮

### 3. 退款方式选项
**文件**: `apps/web-antd/src/views/waterfee/bill/counter-payment/data.ts`

支持的退款方式：
- 现金退款 (CASH)
- 银行卡退款 (BANK_CARD)  
- 微信退款 (WECHAT)
- 支付宝退款 (ALIPAY)

### 4. 业务逻辑处理
- `handleRefund()` 函数处理退款提交
- **核心逻辑**: 调用专用的 `refundDeposit()` 接口
- 支持银行卡退款的额外验证
- 退款成功后自动刷新预存款余额
- 重置表单数据
- 显示成功/失败消息

### 5. 标签页切换支持
- 在 `handleTabChange()` 和 `loadUserData()` 中添加对退款标签页的支持
- 切换到退款标签页时自动加载预存款余额

### 6. 数据清理
- 在 `resetAllTabsData()` 中添加退款表单重置逻辑
- 确保用户切换时数据正确清理

### 7. 样式美化
- 为退款界面添加专门的CSS样式
- 余额显示使用橙色突出显示
- 保持与其他标签页一致的设计风格

## 使用流程
1. 在窗口收费页面查询用户信息
2. 切换到"余额退款"标签页
3. 查看当前预存余额
4. 输入退款金额（不能超过当前余额）
5. 填写退款原因（必填）
6. 选择退款方式
7. 如选择银行卡退款，填写银行卡信息
8. 填写备注（可选）
9. 选择是否强制退款（可选）
10. 点击"退款"按钮提交

## 安全特性
- 退款金额不能超过当前余额
- 必须填写退款原因进行记录
- 银行卡退款需要完整的银行卡信息
- 支持强制退款选项（跳过部分校验）
- 表单验证确保数据完整性
- 退款按钮使用危险样式提醒用户谨慎操作

## 技术特点
- **专用接口**: 使用独立的退款接口，支持复杂的退款场景
- **完整字段**: 支持银行卡退款等需要额外信息的场景
- **动态表单**: 根据退款方式动态显示相关字段
- **强制退款**: 提供强制退款选项，适应特殊业务需求
- 响应式表单设计
- 实时数据验证
- 自动余额刷新
- 统一的错误处理
- 符合项目代码风格和架构规范

## 实现优势
1. **功能完整**: 支持多种退款方式和复杂业务场景
2. **用户友好**: 动态表单提供良好的用户体验
3. **安全可靠**: 完整的验证机制确保退款安全
4. **扩展性强**: 可以轻松添加新的退款方式或字段
5. **维护性好**: 代码结构清晰，易于维护和扩展
