import type { PageQuery } from '#/api/common';
import type {
  BranchOfficeForm,
  BranchOfficeQuery,
  BranchOfficeVO,
} from '#/api/waterfee/branchOffice/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/branchOffice/list',
  root = '/waterfee/branchOffice',
}

/**
 * 营业网点导出
 * @param data data
 * @returns void
 */
export function BranchOfficeExport(data: Partial<BranchOfficeForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询营业网点列表
 * @param params 查询参数
 * @returns 营业网点列表
 */
export function listBranchOffice(params?: BranchOfficeQuery & PageQuery) {
  return requestClient.get<BranchOfficeVO>(Api.list, { params });
}

/**
 * 查询营业网点详细
 * @param branchId 营业网点ID
 * @returns 营业网点信息
 */
export function getBranchOffice(branchId: number | string) {
  return requestClient.get<BranchOfficeForm>(`${Api.root}/${branchId}`);
}

/**
 * 新增营业网点
 * @param data 新增数据
 * @returns void
 */
export function addBranchOffice(data: BranchOfficeForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改营业网点
 * @param data 修改数据
 * @returns void
 */
export function updateBranchOffice(data: BranchOfficeForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除营业网点
 * @param branchId 营业网点ID或ID数组
 * @returns void
 */
export function delBranchOffice(
  branchId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${branchId}`);
}
