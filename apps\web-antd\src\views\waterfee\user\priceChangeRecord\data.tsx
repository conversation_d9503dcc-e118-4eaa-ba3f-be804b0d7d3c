import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userNoOrUserName',
    label: '关键字',
    componentProps: {
      placeholder: '请输入户号或名称',
      allowClear: true,
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'timeRange',
    label: '处理日期',
    componentProps: {
      getPopupContainer,
      placeholder: ['开始日期', '结束日期'],
      valueFormat: 'YYYY-MM-DD',
      allowClear: true,
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: '主键',
  //   field: 'priceChangeId',
  // },
  // {
  //   title: '用户ID',
  //   field: 'userId',
  // },
  {
    title: '户号',
    field: 'userNo',
  },
  {
    title: '用户名',
    field: 'userName',
  },
  // {
  //   title: '原用水性质',
  //   field: 'beforePriceUseWaterNature',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(
  //         row.beforePriceUseWaterNature,
  //         'waterfee_user_use_water_nature',
  //       );
  //     },
  //   },
  // },
  {
    title: '原计费方式',
    field: 'beforeBillingMethod',
    slots: {
      default: ({ row }) => {
        return row.beforeBillingMethodName;
        // return renderDict(
        //   row.beforeBillingMethod,
        //   'waterfee_user_billing_method',
        // );
      },
    },
  },
  // {
  //   title: '原是否有违约金',
  //   field: 'beforeIfPenalty',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(row.beforeIfPenalty, 'yes_no');
  //     },
  //   },
  // },
  // {
  //   title: '原违约金',
  //   field: 'beforePenaltyType',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(row.beforePenaltyType, 'waterfee_user_penalty_type');
  //     },
  //   },
  // },
  // {
  //   title: '原是否有附加费',
  //   field: 'beforeIfExtraCharge',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(row.beforeIfExtraCharge, 'yes_no');
  //     },
  //   },
  // },
  // {
  //   title: '原附加费',
  //   field: 'beforeExtraChargeType',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(
  //         row.beforeExtraChargeType,
  //         'waterfee_user_extra_charge_type',
  //       );
  //     },
  //   },
  // },
  // {
  //   title: '新用水性质',
  //   field: 'afterPriceUseWaterNature',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(
  //         row.afterPriceUseWaterNature,
  //         'waterfee_user_use_water_nature',
  //       );
  //     },
  //   },
  // },
  {
    title: '新计费方式',
    field: 'afterBillingMethod',
    slots: {
      default: ({ row }) => {
        return row.afterBillingMethodName;
        // return renderDict(
        //   row.afterBillingMethod,
        //   'waterfee_user_billing_method',
        // );
      },
    },
  },
  // {
  //   title: '新是否有违约金',
  //   field: 'afterIfPenalty',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(row.afterIfPenalty, 'yes_no');
  //     },
  //   },
  // },
  // {
  //   title: '新违约金',
  //   field: 'afterPenaltyType',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(row.afterPenaltyType, 'waterfee_user_penalty_type');
  //     },
  //   },
  // },
  // {
  //   title: '新是否有附加费',
  //   field: 'afterIfExtraCharge',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(row.afterIfExtraCharge, 'yes_no');
  //     },
  //   },
  // },
  // {
  //   title: '新附加费',
  //   field: 'afterExtraChargeType',
  //   slots: {
  //     default: ({ row }) => {
  //       return renderDict(
  //         row.afterExtraChargeType,
  //         'waterfee_user_extra_charge_type',
  //       );
  //     },
  //   },
  // },
  {
    title: '处理人',
    field: 'createByUserName',
  },
  {
    title: '处理日期',
    field: 'createTime',
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'priceChangeId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'beforePriceUseWaterNature',
    label: '原价格-用水性质',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_billing_method'),
    },
    fieldName: 'beforeBillingMethod',
    label: '原计费方式',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('yes_no'),
      optionType: 'button',
    },
    fieldName: 'beforeIfPenalty',
    label: '原是否有违约金',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_penalty_type'),
    },
    fieldName: 'beforePenaltyType',
    label: '原违约金类型',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('yes_no'),
      optionType: 'button',
    },
    fieldName: 'beforeIfExtraCharge',
    label: '原是否有附加费',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_extra_charge_type'),
    },
    fieldName: 'beforeExtraChargeType',
    label: '原附加费内容',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'afterPriceUseWaterNature',
    label: '新价格-用水性质',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_billing_method'),
    },
    fieldName: 'afterBillingMethod',
    label: '新计费方式',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('yes_no'),
      optionType: 'button',
    },
    fieldName: 'afterIfPenalty',
    label: '新是否有违约金',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_penalty_type'),
    },
    fieldName: 'afterPenaltyType',
    label: '新违约金类型',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('yes_no'),
      optionType: 'button',
    },
    fieldName: 'afterIfExtraCharge',
    label: '新是否有附加费',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_extra_charge_type'),
    },
    fieldName: 'afterExtraChargeType',
    label: '新附加费内容',
  },
];
