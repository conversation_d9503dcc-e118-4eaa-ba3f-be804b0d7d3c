import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    componentProps: {
      placeholder: '请输入用户ID',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'incomeType',
    label: '收入类型',
    componentProps: {
      placeholder: '请选择收入类型',
      options: [
        { label: '滞纳金', value: 'LATE_FEE' },
        { label: '违约金', value: 'PENALTY' },
        { label: '手续费', value: 'SERVICE_FEE' },
        { label: '其他收入', value: 'OTHER' },
      ],
      allowClear: true,
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'amount',
    label: '金额',
    componentProps: {
      placeholder: '请输入金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'incomeTime',
    label: '收入时间',
    componentProps: {
      placeholder: '请选择收入时间',
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '记录编号',
    field: 'id',
  },
  {
    title: '用户ID',
    field: 'userId',
  },
  {
    title: '收入类型',
    field: 'incomeType',
  },
  {
    title: '金额',
    field: 'amount',
  },
  {
    title: '收入时间',
    field: 'incomeTime',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    rules: 'required',
    componentProps: {
      placeholder: '请输入用户ID',
    },
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用户姓名',
    componentProps: {
      placeholder: '请输入用户姓名',
    },
  },
  {
    component: 'Select',
    fieldName: 'incomeType',
    label: '收入类型',
    rules: 'required',
    componentProps: {
      placeholder: '请选择收入类型',
      options: [
        { label: '滞纳金', value: 'LATE_FEE' },
        { label: '违约金', value: 'PENALTY' },
        { label: '手续费', value: 'SERVICE_FEE' },
        { label: '其他收入', value: 'OTHER' },
      ],
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'amount',
    label: '金额',
    rules: 'required',
    componentProps: {
      placeholder: '请输入金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
      addonAfter: '元',
    },
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择收入时间',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'incomeTime',
    label: '收入时间',
    rules: 'required',
  },
  {
    component: 'Select',
    fieldName: 'paymentMethod',
    label: '收款方式',
    componentProps: {
      placeholder: '请选择收款方式',
      options: [
        { label: '现金', value: 'CASH' },
        { label: '银行卡', value: 'BANK_CARD' },
        { label: '微信', value: 'WECHAT' },
        { label: '支付宝', value: 'ALIPAY' },
        { label: '银行转账', value: 'BANK_TRANSFER' },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'incomeSource',
    label: '收入来源',
    componentProps: {
      placeholder: '请输入收入来源',
    },
  },
  {
    component: 'Input',
    fieldName: 'operator',
    label: '操作员',
    componentProps: {
      placeholder: '请输入操作员',
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
];
