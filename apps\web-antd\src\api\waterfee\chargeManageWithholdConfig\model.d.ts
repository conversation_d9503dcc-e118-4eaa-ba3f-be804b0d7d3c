export interface ChargeManageWithholdConfigVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 
   */
  userId: string | number;

  /**
   * 
   */
  bankAccount: string;

  /**
   * 
   */
  bankName: string;

  /**
   * 是否签约
   */
  signed: number;

  /**
   * 签约时间
   */
  signTime: string;

  /**
   * 取消时间
   */
  cancelTime: string;

  /**
   * 
   */
  remark: string;

}

export interface ChargeManageWithholdConfigForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 
   */
  userId?: string | number;

  /**
   * 
   */
  bankAccount?: string;

  /**
   * 
   */
  bankName?: string;

  /**
   * 是否签约
   */
  signed?: number;

  /**
   * 签约时间
   */
  signTime?: string;

  /**
   * 取消时间
   */
  cancelTime?: string;

  /**
   * 
   */
  remark?: string;

}

export interface ChargeManageWithholdConfigQuery extends PageQuery {

  /**
   * 
   */
  userId?: string | number;

  /**
   * 
   */
  bankAccount?: string;

  /**
   * 
   */
  bankName?: string;

  /**
   * 是否签约
   */
  signed?: number;

  /**
   * 签约时间
   */
  signTime?: string;

  /**
   * 取消时间
   */
  cancelTime?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



