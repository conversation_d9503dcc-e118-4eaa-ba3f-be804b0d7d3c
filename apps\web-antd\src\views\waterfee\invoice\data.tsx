import type { VbenFormSchema } from '@vben/common-ui';

import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { redFlushNumOptions } from '#/api/waterfee/invoice';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'billNumber',
    label: '账单编号',
  },
  {
    component: 'Input',
    fieldName: 'buyerName',
    label: '发票抬头',
  },
  {
    component: 'Select',
    fieldName: 'invoiceType',
    label: '发票类型',
    componentProps: {
      options: [
        { label: '蓝票', value: '1' },
        { label: '红票', value: '2' },
      ],
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'clerk',
    label: '开票员',
  },
  {
    component: 'Select',
    fieldName: 'invoiceStatus',
    label: '开票状态',
    componentProps: {
      options: [
        { label: '开票中', value: 'process' },
        { label: '开票成功', value: 'success' },
        { label: '开票失败', value: 'failure' },
        { label: '已作废', value: 'cancel' },
      ],
      allowClear: true,
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'invoiceTime',
    label: '开票时间',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // { field: 'invoiceId', title: '发票ID', minWidth: 100 },
  { field: 'billNumber', title: '账单编号', minWidth: 140 },
  { field: 'buyerName', title: '发票抬头', minWidth: 100 },
  {
    field: 'invoiceType',
    title: '发票类型',
    slots: { default: 'invoiceType' },
    minWidth: 100,
  },
  {
    field: 'invoiceStatus',
    title: '开票状态',
    slots: { default: 'invoiceStatus' },
    minWidth: 120,
  },
  {
    field: 'pdfUrl',
    title: 'PDF',
    slots: { default: 'pdfUrl' },
    minWidth: 100,
  },
  {
    field: 'ofdUrl',
    title: 'OFD',
    slots: { default: 'ofdUrl' },
    minWidth: 100,
  },
  {
    field: 'xmlUrl',
    title: 'XML',
    slots: { default: 'xmlUrl' },
    minWidth: 100,
  },
  { field: 'invoiceTime', title: '开票时间', minWidth: 160 },
  { field: 'invoiceCode', title: '发票代码', minWidth: 120 },
  { field: 'invoiceNo', title: '发票号码', minWidth: 120 },
  { field: 'invoiceKind', title: '发票种类', minWidth: 160 },
  { field: 'clerk', title: '开票员', minWidth: 100 },
  { field: 'orderAmount', title: '价税合计', minWidth: 120 },
  { field: 'exTaxAmount', title: '不含税金额', minWidth: 120 },
  { field: 'taxAmount', title: '含税金额', minWidth: 120 },
  { field: 'serialNo', title: '流水号', minWidth: 180 },
  // { field: 'failCause', title: '失败原因', minWidth: 120 },
  {
    field: 'action',
    title: '操作',
    fixed: 'right',
    slots: { default: 'action' },
    width: 200,
  },
];

export const invoiceFormSchema = (
  billOptions: { label: string; value: string }[] = [],
): VbenFormSchema[] => [
  {
    component: 'RadioGroup',
    fieldName: 'isEnterprise',
    label: '是否企业',
    rules: 'required',
    componentProps: {
      options: [
        { label: '企业', value: true },
        { label: '个人', value: false },
      ],
      optionType: 'button',
      buttonStyle: 'solid',
    },
  },
  {
    component: 'Input',
    fieldName: 'buyerName',
    label: '购方名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'buyerTaxNumber',
    label: '购方税号',
    // 选择企业时，此项为必填项
    dependencies: {
      show: (values: any) => values.isEnterprise,
      triggerFields: ['isEnterprise'],
    },
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    fieldName: 'invoiceType',
    label: '发票类型',
    defaultValue: '1',
    rules: 'required',
    componentProps: {
      options: [
        { label: '蓝票', value: '1' },
        { label: '红票', value: '2' },
      ],
      optionType: 'button',
      buttonStyle: 'solid',
    },
  },
  {
    component: 'Select',
    fieldName: 'billNumber',
    label: '关联账单编号',
    rules: 'required',
    componentProps: {
      options: billOptions,
      showSearch: true,
      allowClear: true,
      placeholder: '请选择账单编号',
    },
    // 选择发票类型为红票时，不展示此项
    dependencies: {
      show: (values: any) => values.invoiceType !== '2',
      triggerFields: ['invoiceType'],
    },
  },
  // {
  //   component: 'Input',
  //   fieldName: 'invoiceCode',
  //   label: '发票代码',
  // },
  // {
  //   component: 'Input',
  //   fieldName: 'invoiceNum',
  //   label: '发票号码',
  // },
  // 当选择红票时，此项为必填项
  {
    component: 'ApiSelect',
    fieldName: 'invoiceId',
    label: '红冲蓝票号',
    dependencies: {
      show: (values: any) => values.invoiceType === '2',
      triggerFields: ['invoiceType'],
    },
    componentProps: () => {
      return {
        api: redFlushNumOptions,
        fieldNames: { label: 'invoiceNo', value: 'invoiceId' },
        params: {
          invoiceType: '1',
        },
      };
    },
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'clerk',
    label: '开票员',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    fieldName: 'pushMode',
    label: '推送方式',
    rules: 'required',
    componentProps: {
      options: [
        { label: '不推送', value: '-1' },
        { label: '邮箱', value: '0' },
        { label: '手机', value: '1' },
        { label: '邮箱、手机', value: '2' },
      ],
      optionType: 'button',
      buttonStyle: 'solid',
    },
  },
  {
    component: 'Input',
    fieldName: 'buyerPhone',
    label: '购方手机',
    // 选择推送方式为手机时，此项为必填项
    dependencies: {
      show: (values: any) => values.pushMode === '1' || values.pushMode === '2',
      triggerFields: ['pushMode'],
    },
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'email',
    label: '购方邮箱',
    // 选择推送方式为邮箱时，此项为必填项
    dependencies: {
      show: (values: any) => values.pushMode === '0' || values.pushMode === '2',
      triggerFields: ['pushMode'],
    },
    rules: 'required',
  },
];
