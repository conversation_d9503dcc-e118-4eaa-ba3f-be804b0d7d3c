<script setup lang="ts">
import type { PublicInfoModel } from '#/api/waterfee/model/publicInfoModel';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import {
  publicInfoAdd,
  publicInfoDetail,
  publicInfoUpdate,
} from '#/api/waterfee/publicInfo';

import { formSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? '编辑停水通知' : '新增停水通知';
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  schema: formSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

// 获取只读字段的表单配置
function getReadonlyFields() {
  // 只有在编辑模式下才添加只读字段
  if (!isUpdate.value) return [];

  return [
    {
      component: 'Input',
      fieldName: 'publisher',
      label: '发布人',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'publishTime',
      label: '发布时间',
      componentProps: {
        disabled: true,
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'viewCount',
      label: '点击量',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        disabled: true,
        options: [
          { label: '未发布', value: '0' },
          { label: '已发布', value: '1' },
        ],
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'createTime',
      label: '创建时间',
      componentProps: {
        disabled: true,
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'updateTime',
      label: '更新时间',
      componentProps: {
        disabled: true,
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
    },
  ];
}

// 更新表单 schema
async function updateFormSchema() {
  if (isUpdate.value) {
    // 编辑模式，添加只读字段
    const readonlyFields = getReadonlyFields();
    const baseSchema = formSchema();
    formApi.updateSchema([...baseSchema, ...readonlyFields]);
  } else {
    // 新增模式，使用基础表单
    formApi.updateSchema(formSchema());
  }
}

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleSubmit,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    try {
      const { id } = drawerApi.getData() as { id?: number | string };
      isUpdate.value = !!id;

      // 更新表单 schema
      await updateFormSchema();

      if (isUpdate.value && id) {
        // 编辑模式，获取详情并设置表单值
        const record = await publicInfoDetail(id);
        console.log('获取详情返回数据:', record);

        // 确保infoId字段存在
        await formApi.setValues({
          ...record,
          infoId: id,
          id,
        });

        console.log('设置表单后的值:', await formApi.getValues());
      } else {
        // 新增模式，重置表单，只设置必要字段
        await formApi.resetForm();
        await formApi.setValues({ infoType: '1' }); // 停水通知类型
      }
    } catch (error) {
      console.error('Error in drawer open:', error);
    } finally {
      drawerApi.drawerLoading(false);
    }
  },
});

async function handleSubmit() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }

    const values = cloneDeep(await formApi.getValues());

    console.log('values', values);

    // 构建提交参数
    const params: Partial<PublicInfoModel> = {
      ...values,
      infoType: '1', // 停水通知
    };

    // 确保编辑时有id字段
    if (isUpdate.value) {
      const { id } = drawerApi.getData() as { id?: number | string };
      params.id = id; // 确保id字段存在
      params.infoId = id; // 同时设置infoId，以防API需要

      console.log('提交参数:', params);

      // 使用表单中的infoId，已经在获取详情时设置
      delete params.publisher;
      delete params.publishTime;
      delete params.viewCount;
      delete params.status;
      delete params.createTime;
      delete params.updateTime;
    }

    // 根据是否为编辑模式调用不同的 API
    await (isUpdate.value ? publicInfoUpdate(params) : publicInfoAdd(params));

    // 通知父组件刷新表格
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error('Error in form submission:', error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer
    :close-on-click-modal="false"
    :title="title"
    :class="isUpdate ? 'w-[800px]' : 'w-[700px]'"
  >
    <div class="drawer-content">
      <BasicForm />
    </div>
  </BasicDrawer>
</template>

<style scoped>
.drawer-content {
  padding: 0 12px;
}
</style>
