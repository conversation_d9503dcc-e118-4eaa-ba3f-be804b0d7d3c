import type { ChargeManageNonOperatingIncomeVO, ChargeManageNonOperatingIncomeForm, ChargeManageNonOperatingIncomeQuery } from '#/api/waterfee/chargeManageNonOperatingIncome/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/chargeManageNonOperatingIncome',
  list = '/waterfee/chargeManageNonOperatingIncome/list'
}

/**
 * 营业外收入记录导出
 * @param data data
 * @returns void
 */
export function ChargeManageNonOperatingIncomeExport(data: Partial<ChargeManageNonOperatingIncomeForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询营业外收入记录列表
 * @param params 查询参数
 * @returns 营业外收入记录列表
 */
export function listChargeManageNonOperatingIncome(params?: ChargeManageNonOperatingIncomeQuery & PageQuery) {
  return requestClient.get<ChargeManageNonOperatingIncomeVO>(Api.list, { params });
}

/**
 * 查询营业外收入记录详细
 * @param id 营业外收入记录ID
 * @returns 营业外收入记录信息
 */
export function getChargeManageNonOperatingIncome(id: string | number) {
  return requestClient.get<ChargeManageNonOperatingIncomeForm>(`${Api.root}/${id}`);
}

/**
 * 新增营业外收入记录
 * @param data 新增数据
 * @returns void
 */
export function addChargeManageNonOperatingIncome(data: ChargeManageNonOperatingIncomeForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改营业外收入记录
 * @param data 修改数据
 * @returns void
 */
export function updateChargeManageNonOperatingIncome(data: ChargeManageNonOperatingIncomeForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除营业外收入记录
 * @param id 营业外收入记录ID或ID数组
 * @returns void
 */
export function delChargeManageNonOperatingIncome(id: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
