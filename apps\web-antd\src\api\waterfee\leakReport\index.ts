import type { PageQuery } from '#/api/common';
import type {
  LeakReportForm,
  LeakReportQuery,
  LeakReportVO,
} from '#/api/waterfee/leakReport/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/leakReport/list',
  root = '/waterfee/leakReport',
}

/**
 * 偷漏水举报导出
 * @param data data
 * @returns void
 */
export function LeakReportExport(data: Partial<LeakReportForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询偷漏水举报列表
 * @param params 查询参数
 * @returns 偷漏水举报列表
 */
export function listLeakReport(params?: LeakReportQuery & PageQuery) {
  return requestClient.get<LeakReportVO>(Api.list, { params });
}

/**
 * 查询偷漏水举报详细
 * @param reportId 偷漏水举报ID
 * @returns 偷漏水举报信息
 */
export function getLeakReport(reportId: number | string) {
  return requestClient.get<LeakReportForm>(`${Api.root}/${reportId}`);
}

/**
 * 新增偷漏水举报
 * @param data 新增数据
 * @returns void
 */
export function addLeakReport(data: LeakReportForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改偷漏水举报
 * @param data 修改数据
 * @returns void
 */
export function updateLeakReport(data: LeakReportForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除偷漏水举报
 * @param reportId 偷漏水举报ID或ID数组
 * @returns void
 */
export function delLeakReport(
  reportId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${reportId}`);
}
