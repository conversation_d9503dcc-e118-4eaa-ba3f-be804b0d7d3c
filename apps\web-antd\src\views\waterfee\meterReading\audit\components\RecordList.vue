<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';

import {
  CheckOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  SaveOutlined,
} from '@ant-design/icons-vue';
import { Button, Input, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { areaInfo } from '#/api/waterfee/area';
import { meterInfoByNo } from '#/api/waterfee/meter';
import { meterBookInfo } from '#/api/waterfee/meterbook';
import {
  auditRecord,
  batchCancelPendingRecord,
  batchPendingRecord,
} from '#/api/waterfee/meterReadingRecord';
import {
  getIntelligentMeterReadingList,
  getMeterReadingDetailList,
  updateMeterReading,
} from '#/api/waterfee/meterReadingRecord/audit';
import { getUser } from '#/api/waterfee/user/archivesManage';

import { recordColumns } from '../audit.data';

const props = defineProps({
  meterBookId: {
    type: String,
    required: true,
  },
  taskId: {
    type: String,
    required: true,
  },
  bookData: {
    type: Object,
    default: () => ({}),
  },
  searchMeterNo: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['reload']);

// 加载状态
const loading = ref(false);
// 内部加载状态标志（避免重复加载）
const isLoading = ref(false);
// 记录列表
const recordList = ref([]);
// 原始记录列表（未过滤）
const originalRecordList = ref([]);
// 选中的记录ID
const selectedRecordIds = ref([]);

// 计算是否有选中记录
const hasSelectedRecords = computed(() => {
  return selectedRecordIds.value.length > 0;
});

// 挂起原因
const pendingReason = ref('');
// 挂起对话框可见性
const pendingModalVisible = ref(false);

// 修改过的记录
const modifiedRecords = ref([]);

// 计算是否有修改过的记录
const hasModifiedRecords = computed(() => {
  return modifiedRecords.value.length > 0;
});

// 计算是否有挂起的记录
const hasPendingRecords = computed(() => {
  if (selectedRecordIds.value.length === 0) return false;
  const selectedRecords = tableApi.grid?.getCheckboxRecords?.() || [];
  return selectedRecords.some((record) => record.isPending === '1');
});

// 计算是否有未挂起的记录
const hasNonPendingRecords = computed(() => {
  if (selectedRecordIds.value.length === 0) return false;
  const selectedRecords = tableApi.grid?.getCheckboxRecords?.() || [];
  return selectedRecords.some((record) => record.isPending !== '1');
});

// 缓存对象
const cache = reactive({
  taskInfo: {}, // 缓存任务信息，key为taskId
  meterInfo: {}, // 缓存水表信息，key为meterNo
  areaInfo: {}, // 缓存区域信息，key为businessAreaId
  bookInfo: {}, // 缓存表册信息，key为meterBookId
  userInfo: {}, // 缓存用户信息，key为userId
});

// 防抖定时器和状态变量
let loadTimer = null;
let lastLoadTime = 0; // 上次加载时间

// 监听属性变化
watch(
  [() => props.meterBookId, () => props.taskId],
  async ([newBookId, newTaskId], [oldBookId, oldTaskId]) => {
    // 只有当两个值都存在且至少有一个值发生变化时才加载数据
    if (
      newBookId &&
      newTaskId &&
      (newBookId !== oldBookId || newTaskId !== oldTaskId)
    ) {
      // 防止重复加载
      if (loading.value) return;

      // 使用字符串比较，避免引用比较问题
      const newBookIdStr = String(newBookId);
      const oldBookIdStr = oldBookId ? String(oldBookId) : '';
      const newTaskIdStr = String(newTaskId);
      const oldTaskIdStr = oldTaskId ? String(oldTaskId) : '';

      if (newBookIdStr === oldBookIdStr && newTaskIdStr === oldTaskIdStr) {
        console.log('属性值相同，不重新加载');
        return;
      }

      console.log(
        `属性变化: meterBookId ${oldBookIdStr} -> ${newBookIdStr}, taskId ${oldTaskIdStr} -> ${newTaskIdStr}`,
      );
      await loadRecordList();
    }
  },
  { immediate: true },
);

// 监听搜索水表编号变化
watch(
  () => props.searchMeterNo,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      console.log(`搜索水表编号变化: ${oldValue} -> ${newValue}`);
      filterRecordsByMeterNo();
    }
  },
);
// 表格配置
const gridOptions = {
  border: true,
  showOverflow: true,
  height: 'auto',
  minHeight: 400,
  maxHeight: 600,
  autoResize: true,
  width: '100%',
  fit: true,
  resizable: true,
  tableLayout: 'fixed',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  scrollY: {
    enabled: true,
    gt: 30,
  },
  columnConfig: {
    resizable: true,
    minWidth: 100,
  },
  keepSource: true,
  rowConfig: {
    keyField: 'recordId',
    isHover: true,
    isCurrent: true,
  },
  checkboxConfig: {
    reserve: true,
    highlight: true,
    trigger: 'row',
  },
  columns: recordColumns,
  editRules: {
    currentReading: [
      { required: true, message: '本期抄表读数不能为空' },
      {
        validator: (rule: any, value: any) => {
          return value >= 0;
        },
        message: '本期抄表读数不能小于0',
      },
    ],
  },
  emptyRender: {
    name: 'EmptyRender',
  },
  proxyConfig: {
    enabled: true,
    ajax: {
      query: async (
        { page }: { page?: { currentPage: number; pageSize: number } },
        _formValues = {},
      ) => {
        if (!props.meterBookId || !props.taskId) {
          message.error('表册信息不完整，无法加载抄表记录');
          return { rows: [], total: 0 };
        }
        try {
          const isIntelligent = props.bookData.meterType === '1';
          const res = await (isIntelligent
            ? getIntelligentMeterReadingList(props.meterBookId, {
                isAudited: props.bookData.isAudited,
                pageNum: page?.currentPage || 1,
                pageSize: page?.pageSize || 100,
              })
            : getMeterReadingDetailList({
                meterBookId: props.meterBookId,
                taskId: props.taskId,
                pageNum: page?.currentPage || 1,
                pageSize: page?.pageSize || 100,
              }));
          // 只在此作用域声明 rows/total
          let rows: any[] = [];
          if (Array.isArray(res?.rows)) {
            rows = res.rows;
          } else if (Array.isArray(res)) {
            rows = res;
          }
          const total =
            typeof res?.total === 'number' ? res.total : rows.length;
          return { rows, total };
        } catch {
          message.error('加载抄表记录失败，请刷新重试');
          return { rows: [], total: 0 };
        }
      },
    },
  },
};

// 使用 useVbenVxeGrid
const [BasicTable, tableApi] = useVbenVxeGrid({
  gridOptions: {
    ...gridOptions,
    loading: loading.value,
    editConfig: {
      trigger: 'click',
      mode: 'cell',
      showStatus: true,
      showUpdateStatus: true,
      beforeEditMethod,
    },
    rowClassName,
  },
  gridEvents: {
    checkboxAll: handleSelectionChange,
    checkboxChange: handleSelectionChange,
    editClosed: handleEditClosed,
  },
});

// 加载抄表记录列表
async function loadRecordList(forceRefresh = false) {
  if (!props.meterBookId || !props.taskId) {
    message.error('表册信息不完整，无法加载抄表记录');
    return;
  }

  // 如果已经在加载中，则不重复加载
  if (loading.value || (isLoading.value && !forceRefresh)) return;

  // 如果距离上次加载时间小于1秒且不是强制刷新，则不重复加载
  const now = Date.now();
  if (now - lastLoadTime < 1000 && !forceRefresh) {
    console.log('加载频率过高，忽略本次加载请求');
    return;
  }

  // 防抖处理，避免短时间内多次调用
  if (loadTimer) clearTimeout(loadTimer);

  loadTimer = setTimeout(async () => {
    isLoading.value = true;
    loading.value = true;
    lastLoadTime = Date.now();

    // 使用 requestAnimationFrame 延迟处理，等待浏览器渲染完成
    await new Promise((resolve) => requestAnimationFrame(resolve));
    // 使用 nextTick 延迟处理，等待 Vue DOM 更新完成
    await nextTick();
    try {
      let res;
      const isIntelligent = props.bookData.meterType === '1';
      console.log(
        '水表类型:',
        props.bookData.meterType,
        '是否智能表:',
        isIntelligent,
      );

      // 根据水表类型选择不同的API
      if (isIntelligent) {
        // 如果是智能表，使用智能表专用API
        console.log('使用智能表API获取抄表记录');
        res = await getIntelligentMeterReadingList(props.meterBookId, {
          isAudited: props.bookData.isAudited,
          pageNum: 1,
          pageSize: 100,
        });
      } else {
        // 否则使用通用API
        console.log('使用通用API获取抄表记录');
        res = await getMeterReadingDetailList({
          meterBookId: props.meterBookId,
          taskId: props.taskId,
          pageNum: 1,
          pageSize: 100,
        });
      }

      if (res && res.rows) {
        originalRecordList.value = res.rows;
        // 应用过滤
        filterRecordsByMeterNo();
        console.log(`成功加载 ${originalRecordList.value.length} 条抄表记录`);

        // 收集需要查询的ID
        const meterNos = new Set();
        const areaIds = new Set();
        const bookIds = new Set();
        const userIds = new Set();

        // 收集所有需要查询的ID
        for (const record of recordList.value) {
          if (record.meterNo && !cache.meterInfo[record.meterNo]) {
            meterNos.add(record.meterNo);
          }
        }

        // 批量获取水表信息
        if (meterNos.size > 0) {
          try {
            const meterPromises = [...meterNos].map((meterNo) =>
              meterInfoByNo(meterNo)
                .then((info) => {
                  if (info) {
                    cache.meterInfo[meterNo] = info;

                    // 收集需要查询的区域ID和表册ID
                    if (
                      info.businessAreaId &&
                      !cache.areaInfo[info.businessAreaId]
                    ) {
                      areaIds.add(info.businessAreaId);
                    }
                    if (info.meterBookId && !cache.bookInfo[info.meterBookId]) {
                      bookIds.add(info.meterBookId);
                    }
                    // 收集需要查询的用户ID
                    if (info.userId && !cache.userInfo[info.userId]) {
                      userIds.add(info.userId);
                    }
                  }
                  return { meterNo, info };
                })
                .catch((error) => {
                  console.error(`获取水表 ${meterNo} 信息失败:`, error);
                  return { meterNo, info: null };
                }),
            );

            await Promise.all(meterPromises);
          } catch (error) {
            console.error('批量获取水表信息失败:', error);
          }
        }

        // 批量获取用户信息
        if (userIds.size > 0) {
          try {
            const userPromises = [...userIds].map((userId) =>
              getUser(userId)
                .then((info) => {
                  if (info) {
                    cache.userInfo[userId] = info;
                  }
                  return { userId, info };
                })
                .catch((error) => {
                  console.error(`获取用户 ${userId} 信息失败:`, error);
                  return { userId, info: null };
                }),
            );

            await Promise.all(userPromises);
          } catch (error) {
            console.error('批量获取用户信息失败:', error);
          }
        }

        // 批量获取区域信息
        if (areaIds.size > 0) {
          try {
            const areaPromises = [...areaIds].map((areaId) =>
              areaInfo(areaId)
                .then((info) => {
                  if (info) {
                    cache.areaInfo[areaId] = info;
                  }
                  return { areaId, info };
                })
                .catch((error) => {
                  console.error(`获取区域 ${areaId} 信息失败:`, error);
                  return { areaId, info: null };
                }),
            );

            await Promise.all(areaPromises);
          } catch (error) {
            console.error('批量获取区域信息失败:', error);
          }
        }

        // 批量获取表册信息
        if (bookIds.size > 0) {
          try {
            const bookPromises = [...bookIds].map((bookId) =>
              meterBookInfo(bookId)
                .then((info) => {
                  if (info) {
                    cache.bookInfo[bookId] = info;
                  }
                  return { bookId, info };
                })
                .catch((error) => {
                  console.error(`获取表册 ${bookId} 信息失败:`, error);
                  return { bookId, info: null };
                }),
            );

            await Promise.all(bookPromises);
          } catch (error) {
            console.error('批量获取表册信息失败:', error);
          }
        }

        // 为每条记录填充额外信息
        for (const record of recordList.value) {
          if (record.meterNo) {
            const meterInfo = cache.meterInfo[record.meterNo];
            if (meterInfo) {
              // 更新记录中的字段
              record.businessAreaId = meterInfo.businessAreaId;
              record.meterBookId = meterInfo.meterBookId;
              record.userId = meterInfo.userId;

              // 填充区域信息
              record.businessAreaName =
                meterInfo.businessAreaId &&
                cache.areaInfo[meterInfo.businessAreaId]
                  ? cache.areaInfo[meterInfo.businessAreaId].areaName
                  : meterInfo.businessAreaName;

              // 填充表册信息
              record.meterBookName =
                meterInfo.meterBookId && cache.bookInfo[meterInfo.meterBookId]
                  ? cache.bookInfo[meterInfo.meterBookId].bookName
                  : meterInfo.meterBookName;

              // 填充用户信息
              if (meterInfo.userId && cache.userInfo[meterInfo.userId]) {
                const userInfo = cache.userInfo[meterInfo.userId];
                record.userNo = userInfo.userNo || '';
                record.userName = userInfo.userName || '';
              }
            }
          }
        }
      } else {
        recordList.value = [];
        message.warning('没有找到抄表记录');
      }

      console.log('抄表记录列表:', recordList.value);

      // 强制刷新表格
      if (forceRefresh && tableApi.grid) {
        nextTick(() => {
          if (tableApi.grid.refreshData) {
            tableApi.grid.refreshData();
          }
          if (tableApi.grid.refreshColumn) {
            tableApi.grid.refreshColumn();
          }
          if (tableApi.grid.reloadData) {
            tableApi.grid.reloadData(recordList.value);
          }
        });
      }
    } catch (error) {
      console.error('加载抄表记录列表失败:', error);
      message.error('加载抄表记录列表失败，请刷新重试');

      // 如果API调用失败，使用模拟数据（仅用于开发测试）
      if (import.meta.env.MODE !== 'production') {
        console.log('API调用失败');
      }
    } finally {
      loading.value = false;
      // 延迟重置状态，避免短时间内重复加载
      setTimeout(() => {
        isLoading.value = false;
      }, 500);
    }
  }, 100); // 100ms 防抖延迟
}

// 处理选择变化
function handleSelectionChange(params) {
  // VXE表格的checkbox-change事件传递的是一个对象，包含records属性
  const records = params && params.records ? params.records : [];
  selectedRecordIds.value = records.map((item) => item.recordId);
  console.log('选中的记录ID:', selectedRecordIds.value);
}

// 处理审核
async function handleAudit() {
  if (!hasSelectedRecords.value) {
    message.warning('请选择要审核的记录');
    return;
  }

  try {
    loading.value = true;

    // 获取选中的行
    const selectRecords = tableApi.grid?.getCheckboxRecords?.();
    if (!selectRecords || selectRecords.length === 0) {
      message.warning('请选择要审核的记录');
      return;
    }

    // 筛选出未审核的记录
    const unAuditedRecords = selectRecords.filter(
      (record) => record.isAudited !== '1',
    );

    if (unAuditedRecords.length === 0) {
      message.warning('选中的记录均已审核');
      return;
    }

    // 批量审核未审核的记录
    for (const record of unAuditedRecords) {
      await auditRecord(record.recordId);
    }

    message.success('审核成功');

    // 重新加载数据
    // await loadRecordList();
    tableApi.query();

    // 通知父组件刷新
    emit('reload');
  } catch (error) {
    console.error('审核失败:', error);
    message.error('审核失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 处理单元格编辑完成事件
function handleEditClosed({ row, column }) {
  console.log('编辑完成:', row, column);

  // 如果是本期抄表读数列
  if (column && column.property === 'currentReading') {
    // 检查该记录是否已在修改列表中
    const existingIndex = modifiedRecords.value.findIndex(
      (r) => r.recordId === row.recordId,
    );

    if (existingIndex === -1) {
      // 添加新的修改记录
      modifiedRecords.value.push({ ...row });
    } else {
      // 更新已存在的记录
      modifiedRecords.value[existingIndex] = { ...row };
    }

    console.log('修改后的记录列表:', modifiedRecords.value);
  }
}

// 处理保存
async function handleSave() {
  // 检查是否有修改的记录
  if (!hasModifiedRecords.value) {
    message.info('没有需要保存的修改');
    return;
  }

  try {
    loading.value = true;

    // 保存每条修改的记录
    for (const record of modifiedRecords.value) {
      console.log('保存记录:', record);
      await updateMeterReading({
        recordId: record.recordId,
        currentReading: record.currentReading,
      });
    }

    message.success('保存成功');

    // 清空修改记录列表
    modifiedRecords.value = [];

    // 重新加载数据
    await loadRecordList();

    // 通知父组件刷新
    emit('reload');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 打开挂起对话框
function openPendingModal() {
  if (!hasSelectedRecords.value) {
    message.warning('请选择要挂起的记录');
    return;
  }

  // 检查选中的记录是否已经全部挂起
  const selectedRecords = tableApi.grid?.getCheckboxRecords?.();
  const allPending = selectedRecords.every(
    (record) => record.isPending === '1',
  );

  if (allPending) {
    message.warning('选中的记录均已挂起');
    return;
  }

  pendingReason.value = '';
  pendingModalVisible.value = true;
}

// 强制刷新表格视图
// function forceRefreshUI() {
//   // 强制Vue更新视图
//   nextTick(() => {
//     // 创建一个临时变量，用于触发Vue的响应式更新
//     const tempList = [...recordList.value];
//     recordList.value = [];

//     // 在下一个tick中恢复数据，触发二次渲染
//     nextTick(() => {
//       recordList.value = tempList;

//       // 确保表格组件也刷新
//       if (tableApi.grid && tableApi.grid.reloadData) {
//         tableApi.grid.reloadData(tempList);
//       }
//     });
//   });
// }

// 处理挂起
async function handlePending() {
  if (!pendingReason.value.trim()) {
    message.warning('请输入挂起原因');
    return;
  }

  try {
    loading.value = true;

    // 获取选中的行
    const selectedRecords = tableApi.grid?.getCheckboxRecords?.();
    if (!selectedRecords || selectedRecords.length === 0) {
      message.warning('请选择要挂起的记录');
      return;
    }

    // 筛选出未挂起的记录
    const nonPendingRecords = selectedRecords.filter(
      (record) => record.isPending !== '1',
    );

    if (nonPendingRecords.length === 0) {
      message.warning('选中的记录均已挂起');
      return;
    }

    // 获取未挂起记录的ID
    const recordIds = nonPendingRecords.map((record) => record.recordId);

    // 批量挂起
    await batchPendingRecord(recordIds, pendingReason.value);

    message.success('挂起成功');
    pendingModalVisible.value = false;
    // 重新加载数据
    tableApi.query();
    // 直接更新本地数据，立即反映到UI上
    // 将选中的记录状态设置为挂起
    // for (const record of nonPendingRecords) {
    //   record.isPending = '1';
    // }

    // // 处理原始记录列表中的记录
    // originalRecordList.value.forEach((record) => {
    //   if (recordIds.includes(record.recordId)) {
    //     record.isPending = '1';
    //   }
    // });

    // // 处理当前展示的记录列表
    // recordList.value.forEach((record) => {
    //   if (recordIds.includes(record.recordId)) {
    //     record.isPending = '1';
    //   }
    // });

    // // 强制刷新UI
    // forceRefreshUI();

    // // 清空选中项
    // if (tableApi.grid && tableApi.grid.clearCheckboxRow) {
    //   tableApi.grid.clearCheckboxRow(true);
    // }

    // // 清空选中的记录ID数组
    // selectedRecordIds.value = [];

    // // 强制重新加载数据
    // await loadRecordList(true);

    // // 强制刷新表格
    // nextTick(() => {
    //   // 多重保障的表格刷新策略
    //   if (tableApi.grid) {
    //     // 方法1: 刷新表格数据
    //     if (tableApi.grid.refreshData) {
    //       tableApi.grid.refreshData();
    //     }
    //     // 方法2: 刷新表格列
    //     if (tableApi.grid.refreshColumn) {
    //       tableApi.grid.refreshColumn();
    //     }
    //     // 方法3: 重新加载数据到表格
    //     if (tableApi.grid.reloadData) {
    //       tableApi.grid.reloadData(recordList.value);
    //     }
    //     // 方法4: 刷新整个表格
    //     if (tableApi.grid.refresh) {
    //       tableApi.grid.refresh();
    //     }
    //     // 方法5: 手动触发更新
    //     if (tableApi.grid.updateData) {
    //       tableApi.grid.updateData();
    //     }
    //   }
    // });

    // 通知父组件刷新
    emit('reload');
  } catch (error) {
    console.error('挂起失败:', error);
    message.error('挂起失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 处理取消挂起
async function handleCancelPending() {
  if (!hasSelectedRecords.value) {
    message.warning('请选择要取消挂起的记录');
    return;
  }

  try {
    loading.value = true;

    // 获取选中的行
    const selectedRecords = tableApi.grid?.getCheckboxRecords?.();
    if (!selectedRecords || selectedRecords.length === 0) {
      message.warning('请选择要取消挂起的记录');
      return;
    }

    // 筛选出已挂起的记录
    const pendingRecords = selectedRecords.filter(
      (record) => record.isPending === '1',
    );

    if (pendingRecords.length === 0) {
      message.warning('选中的记录均未挂起');
      return;
    }

    // 获取已挂起记录的ID
    const recordIds = pendingRecords.map((record) => record.recordId);

    // 批量取消挂起
    await batchCancelPendingRecord(recordIds);

    message.success('取消挂起成功');
    // 重新加载数据
    tableApi.query();

    // 直接更新本地数据，立即反映到UI上
    // 将选中的记录状态设置为未挂起
    // for (const record of pendingRecords) {
    //   record.isPending = '0';
    // }

    // // 处理原始记录列表中的记录
    // originalRecordList.value.forEach((record) => {
    //   if (recordIds.includes(record.recordId)) {
    //     record.isPending = '0';
    //   }
    // });

    // // 处理当前展示的记录列表
    // recordList.value.forEach((record) => {
    //   if (recordIds.includes(record.recordId)) {
    //     record.isPending = '0';
    //   }
    // });

    // // 强制刷新UI
    // forceRefreshUI();

    // // 清空选中项
    // if (tableApi.grid && tableApi.grid.clearCheckboxRow) {
    //   tableApi.grid.clearCheckboxRow(true);
    // }

    // // 清空选中的记录ID数组
    // selectedRecordIds.value = [];

    // // 强制重新加载数据
    // await loadRecordList(true);

    // // 强制刷新表格
    // nextTick(() => {
    //   // 多重保障的表格刷新策略
    //   if (tableApi.grid) {
    //     // 方法1: 刷新表格数据
    //     if (tableApi.grid.refreshData) {
    //       tableApi.grid.refreshData();
    //     }
    //     // 方法2: 刷新表格列
    //     if (tableApi.grid.refreshColumn) {
    //       tableApi.grid.refreshColumn();
    //     }
    //     // 方法3: 重新加载数据到表格
    //     if (tableApi.grid.reloadData) {
    //       tableApi.grid.reloadData(recordList.value);
    //     }
    //     // 方法4: 刷新整个表格
    //     if (tableApi.grid.refresh) {
    //       tableApi.grid.refresh();
    //     }
    //     // 方法5: 手动触发更新
    //     if (tableApi.grid.updateData) {
    //       tableApi.grid.updateData();
    //     }
    //   }
    // });

    // 通知父组件刷新
    emit('reload');
  } catch (error) {
    console.error('取消挂起失败:', error);
    message.error('取消挂起失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 根据水表编号过滤记录
function filterRecordsByMeterNo() {
  if (!props.searchMeterNo || !props.searchMeterNo.trim()) {
    // 如果搜索条件为空，显示所有记录
    recordList.value = [...originalRecordList.value];
  } else {
    // 否则根据水表编号过滤
    const searchTerm = props.searchMeterNo.trim().toLowerCase();
    recordList.value = originalRecordList.value.filter((record) => {
      return (
        record.meterNo && record.meterNo.toLowerCase().includes(searchTerm)
      );
    });
  }
  console.log(`过滤后的记录数: ${recordList.value.length}`);
}

// 根据行数据设置行的样式类名
function rowClassName({ row }) {
  // 根据挂起状态和审核状态返回不同的类名
  if (row.isPending === '1') {
    return 'pending-row';
  }
  if (row.isAudited === '1') {
    return 'audited-row';
  }
  return 'unaudited-row';
}

// 编辑前的检查方法
function beforeEditMethod({ row, column }) {
  // 如果是本期抄表读数列且记录已审核或已挂起，则不允许编辑
  if (column.property === 'currentReading') {
    if (row.isAudited === '1') {
      message.warning('已审核的记录不可编辑');
      return false;
    }
    if (row.isPending === '1') {
      message.warning('已挂起的记录不可编辑');
      return false;
    }
  }
  return true;
}

// 组件挂载时
onMounted(() => {
  console.log('组件挂载完成');
  // 不使用 connect 方法，避免 $xeToolbar.syncUpdate 错误
});
</script>

<template>
  <div>
    <div class="mb-4">
      <Space>
        <Button
          type="primary"
          :disabled="!hasSelectedRecords"
          @click="handleAudit"
        >
          <template #icon><CheckOutlined /></template>
          审核
        </Button>
        <Button
          type="default"
          :disabled="!hasNonPendingRecords"
          @click="openPendingModal"
        >
          <template #icon><PauseCircleOutlined /></template>
          挂起
        </Button>
        <Button
          type="default"
          :disabled="!hasPendingRecords"
          @click="handleCancelPending"
        >
          <template #icon><PlayCircleOutlined /></template>
          取消挂起
        </Button>
        <Button @click="handleSave">
          <template #icon><SaveOutlined /></template>
          保存
        </Button>
      </Space>
    </div>

    <BasicTable @reload="tableApi.query()" />

    <!-- 挂起原因对话框 -->
    <Modal
      v-model:visible="pendingModalVisible"
      title="挂起抄表记录"
      @ok="handlePending"
      ok-text="确认"
      cancel-text="取消"
    >
      <p>请输入挂起原因：</p>
      <Input
        v-model:value="pendingReason"
        placeholder="请输入挂起原因"
        allow-clear
      />
    </Modal>
  </div>
</template>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

:deep(.vxe-table--render-default .vxe-body--row) {
  height: 48px;
}

:deep(.vxe-table--render-default .vxe-body--column) {
  padding: 12px 8px;
}

:deep(.vxe-table--render-default .vxe-header--column) {
  padding: 12px 8px;
  font-weight: 600;
}

:deep(.vxe-input--inner) {
  padding: 8px;
}

:deep(.vxe-table--render-default) {
  font-size: 14px;
}

/* 加深选中行的背景色 */
:deep(.vxe-table--render-default .vxe-body--row.row--checked) {
  background-color: #e6f7ff !important;
}

:deep(.vxe-table--render-default .vxe-body--row.row--checked:hover) {
  background-color: #bae7ff !important;
}

/* 普通行样式 - 白底 */
:deep(.audited-row),
:deep(.unaudited-row),
:deep(.pending-row) {
  background-color: #fff;
}

:deep(.audited-row:hover),
:deep(.unaudited-row:hover) {
  background-color: #f5f5f5 !important;
}

/* 已审核记录的样式 */
:deep(.vxe-table--render-default .vxe-body--row.audited-row) {
  background-color: #fafafa;
}

:deep(.vxe-table--render-default .vxe-body--row.audited-row:hover) {
  background-color: #f0f0f0 !important;
}

/* 已挂起记录的样式 */
:deep(.vxe-table--render-default .vxe-body--row.pending-row) {
  background-color: #fff1f0;
}

:deep(.vxe-table--render-default .vxe-body--row.pending-row:hover) {
  background-color: #ffccc7 !important;
}

/* 已审核记录中不可编辑字段的样式 */
:deep(.vxe-table--render-default .vxe-body--row.audited-row .vxe-body--column),
:deep(.vxe-table--render-default .vxe-body--row.pending-row .vxe-body--column) {
  color: #999;
}

/* 确保表格占满整个宽度 */
:deep(.vxe-table--main-wrapper) {
  width: 100%;
}

:deep(.vxe-table--body-wrapper) {
  width: 100%;
}

:deep(.vxe-table--header-wrapper) {
  width: 100%;
}

:deep(.vxe-table) {
  width: 100% !important;
}

:deep(.vxe-table--body) {
  width: 100% !important;
}
</style>
