<script setup lang="ts">
import type { UserForm } from '#/api/waterfee/user/archivesManage/model';

import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { getUser } from '#/api/waterfee/user/archivesManage';

import UserDetail from './user-detail.vue';

const userDescription = ref<UserForm>();

const [BasicDrawer, drawerApi] = useVbenDrawer({
  title: '用户详情',
  showConfirmButton: false, // 不显示确认按钮
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    try {
      drawerApi.drawerLoading(true);
      const { row } = drawerApi.getData() as { row?: any };

      if (row && row.userId) {
        try {
          const res = await getUser(row.userId);
          userDescription.value = res;
        } catch (error) {
          console.error('获取用户信息失败', error);
          message.error('获取用户信息失败');
        }
      } else {
        message.warning('未获取到用户ID');
      }
    } catch (error) {
      console.error('详情抽屉打开失败', error);
      message.error('详情抽屉打开失败');
    } finally {
      drawerApi.drawerLoading(false);
    }
  },
});
</script>

<template>
  <BasicDrawer
    :close-on-click-modal="false"
    :destroy-on-close="true"
    class="w-[800px]"
    :show-footer="false"
  >
    <UserDetail v-if="userDescription" :data="userDescription" />
  </BasicDrawer>
</template>
