import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

// 表格列定义
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    title: '序号',
    field: 'rowIndex',
    width: 80,
    align: 'center',
    cellRender: { name: 'SafeIndex' },
  },
  {
    title: '信息标题',
    field: 'title',
    align: 'center',
  },
  {
    title: '发布人',
    field: 'publisher',
    align: 'center',
  },
  {
    title: '发布时间',
    field: 'publishTime',
    align: 'center',
  },
  {
    title: '截止时间',
    field: 'endTime',
    align: 'center',
  },
  {
    title: '点击量',
    field: 'viewCount',
    align: 'center',
  },
  {
    title: '状态',
    field: 'status',
    align: 'center',
    slots: {
      default: ({ row }) => {
        const status = row.status === '1' ? '已发布' : '未发布';
        const color = row.status === '1' ? '#52c41a' : '#faad14';
        return h('span', { style: { color } }, status);
      },
    },
  },
  {
    title: '创建时间',
    field: 'createTime',
    align: 'center',
  },
  {
    title: '操作',
    field: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'action',
    },
  },
];

// 查询表单
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'title',
    label: '信息标题',
    componentProps: {
      placeholder: '请输入信息标题',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '未发布', value: '0' },
        { label: '已发布', value: '1' },
      ],
      allowClear: true,
    },
  },
];

// 新增/编辑表单
export const formSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'title',
    label: '信息标题',
    required: true,
    componentProps: {
      maxLength: 100,
      placeholder: '请输入信息标题',
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'endTime',
    label: '截止时间',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择截止时间',
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'sort',
    label: '排序',
    componentProps: {
      min: 0,
      max: 999,
      placeholder: '数字越小越靠前',
    },
  },
  {
    component: 'Upload',
    fieldName: 'attachmentUrl',
    label: '附件',
    componentProps: {
      maxCount: 1,
      accept: '.doc,.docx,.pdf,.xls,.xlsx,.jpg,.jpeg,.png',
      placeholder: '请上传附件',
    },
  },
  {
    component: 'Textarea',
    fieldName: 'content',
    label: '信息内容',
    required: true,
    componentProps: {
      rows: 10,
      maxLength: 2000,
      showCount: true,
      placeholder: '请输入信息内容',
      style: { width: '100%' },
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      rows: 4,
      maxLength: 500,
      placeholder: '请输入备注信息',
    },
  },
];
