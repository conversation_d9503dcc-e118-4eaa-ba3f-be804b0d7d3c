import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    componentProps: {
      placeholder: '请输入用户ID',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '用户编号',
    componentProps: {
      placeholder: '请输入用户编号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'bankAccount',
    label: '银行账号',
    componentProps: {
      placeholder: '请输入银行账号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'bankName',
    label: '开户行',
    componentProps: {
      placeholder: '请输入开户行',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'signed',
    label: '是否签约',
    componentProps: {
      placeholder: '请选择是否签约',
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      allowClear: true,
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'signTime',
    label: '签约时间',
    componentProps: {
      placeholder: '请选择签约时间',
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键',
    field: 'id',
  },
  {
    title: '用户ID',
    field: 'userId',
  },
  {
    title: '银行账号',
    field: 'bankAccount',
  },
  {
    title: '开户行',
    field: 'bankName',
  },
  {
    title: '是否签约',
    field: 'signed',
  },
  {
    title: '签约时间',
    field: 'signTime',
  },
  {
    title: '取消时间',
    field: 'cancelTime',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    rules: 'required',
    componentProps: {
      placeholder: '请输入用户ID',
    },
  },
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '用户编号',
    componentProps: {
      placeholder: '请输入用户编号',
    },
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用户姓名',
    componentProps: {
      placeholder: '请输入用户姓名',
    },
  },
  {
    component: 'Input',
    fieldName: 'bankAccount',
    label: '银行账号',
    rules: 'required',
    componentProps: {
      placeholder: '请输入银行账号',
    },
  },
  {
    component: 'Input',
    fieldName: 'bankName',
    label: '开户行',
    rules: 'required',
    componentProps: {
      placeholder: '请输入开户行',
    },
  },
  {
    component: 'Input',
    fieldName: 'accountName',
    label: '账户名',
    componentProps: {
      placeholder: '请输入账户名',
    },
  },
  {
    component: 'Select',
    fieldName: 'signed',
    label: '是否签约',
    rules: 'required',
    componentProps: {
      placeholder: '请选择是否签约',
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
    },
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择签约时间',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'signTime',
    label: '签约时间',
    dependencies: {
      if(values) {
        return values.signed === '1';
      },
      triggerFields: ['signed'],
    },
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择取消时间',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'cancelTime',
    label: '取消时间',
    dependencies: {
      if(values) {
        return values.signed === '0';
      },
      triggerFields: ['signed'],
    },
  },
  {
    component: 'Input',
    fieldName: 'contactPhone',
    label: '联系电话',
    componentProps: {
      placeholder: '请输入联系电话',
    },
  },
  {
    component: 'Input',
    fieldName: 'operator',
    label: '操作员',
    componentProps: {
      placeholder: '请输入操作员',
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
];
