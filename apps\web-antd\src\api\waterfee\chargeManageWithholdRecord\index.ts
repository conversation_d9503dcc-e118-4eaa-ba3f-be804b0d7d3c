import type { ChargeManageWithholdRecordVO, ChargeManageWithholdRecordForm, ChargeManageWithholdRecordQuery } from '#/api/waterfee/chargeManageWithholdRecord/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/chargeManageWithholdRecord',
  list = '/waterfee/chargeManageWithholdRecord/list'
}

/**
 * 代扣记录导出
 * @param data data
 * @returns void
 */
export function ChargeManageWithholdRecordExport(data: Partial<ChargeManageWithholdRecordForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询代扣记录列表
 * @param params 查询参数
 * @returns 代扣记录列表
 */
export function listChargeManageWithholdRecord(params?: ChargeManageWithholdRecordQuery & PageQuery) {
  return requestClient.get<ChargeManageWithholdRecordVO>(Api.list, { params });
}

/**
 * 查询代扣记录详细
 * @param id 代扣记录ID
 * @returns 代扣记录信息
 */
export function getChargeManageWithholdRecord(id: string | number) {
  return requestClient.get<ChargeManageWithholdRecordForm>(`${Api.root}/${id}`);
}

/**
 * 新增代扣记录
 * @param data 新增数据
 * @returns void
 */
export function addChargeManageWithholdRecord(data: ChargeManageWithholdRecordForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改代扣记录
 * @param data 修改数据
 * @returns void
 */
export function updateChargeManageWithholdRecord(data: ChargeManageWithholdRecordForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除代扣记录
 * @param id 代扣记录ID或ID数组
 * @returns void
 */
export function delChargeManageWithholdRecord(id: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
