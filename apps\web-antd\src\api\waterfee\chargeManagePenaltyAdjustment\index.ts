import type { ChargeManagePenaltyAdjustmentVO, ChargeManagePenaltyAdjustmentForm, ChargeManagePenaltyAdjustmentQuery } from '#/api/waterfee/chargeManagePenaltyAdjustment/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/chargeManagePenaltyAdjustment',
  list = '/waterfee/chargeManagePenaltyAdjustment/list'
}

/**
 * 违约金调整/减免导出
 * @param data data
 * @returns void
 */
export function ChargeManagePenaltyAdjustmentExport(data: Partial<ChargeManagePenaltyAdjustmentForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询违约金调整/减免列表
 * @param params 查询参数
 * @returns 违约金调整/减免列表
 */
export function listChargeManagePenaltyAdjustment(params?: ChargeManagePenaltyAdjustmentQuery & PageQuery) {
  return requestClient.get<ChargeManagePenaltyAdjustmentVO>(Api.list, { params });
}

/**
 * 查询违约金调整/减免详细
 * @param id 违约金调整/减免ID
 * @returns 违约金调整/减免信息
 */
export function getChargeManagePenaltyAdjustment(id: string | number) {
  return requestClient.get<ChargeManagePenaltyAdjustmentForm>(`${Api.root}/${id}`);
}

/**
 * 新增违约金调整/减免
 * @param data 新增数据
 * @returns void
 */
export function addChargeManagePenaltyAdjustment(data: ChargeManagePenaltyAdjustmentForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改违约金调整/减免
 * @param data 修改数据
 * @returns void
 */
export function updateChargeManagePenaltyAdjustment(data: ChargeManagePenaltyAdjustmentForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除违约金调整/减免
 * @param id 违约金调整/减免ID或ID数组
 * @returns void
 */
export function delChargeManagePenaltyAdjustment(id: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
