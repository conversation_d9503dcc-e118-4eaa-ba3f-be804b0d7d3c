# 余额退款显示问题修复说明

## 问题描述
在余额退款功能中，当前预存余额显示为0元，即使用户有预存款余额。

## 问题原因分析

### 1. 标签页切换逻辑缺失
在标签页切换处理函数 `handleTabChange` 中，没有为 `refund` 标签页添加加载预存款余额的逻辑。

### 2. 用户查询后数据加载不完整
在用户查询成功后（`handleSelectUser` 和 `handleSearch` 函数），只加载了窗口收费相关的数据，没有初始化预存款余额。

### 3. 数据加载时机问题
当用户直接切换到退款标签页时，如果之前没有切换过充值标签页，预存款余额就不会被加载。

## 修复方案

### 1. 完善标签页切换逻辑
在两个 `handleTabChange` 相关的 switch 语句中，为 `refund` case 添加 `loadDepositBalance()` 调用：

```javascript
case 'refund': {
  // 余额退款：加载预存款余额
  loadDepositBalance();
  break;
}
```

### 2. 用户查询后立即加载余额
在 `handleSelectUser` 和 `handleSearch` 函数中，用户查询成功后立即加载预存款余额：

```javascript
// 加载窗口收费标签页的数据
loadPaymentData();

// 同时加载预存款余额，以便退款标签页使用
loadDepositBalance();
```

### 3. 增强 loadDepositBalance 函数
优化了 `loadDepositBalance` 函数的错误处理和日志记录。

## 修复后的效果

### 1. 用户查询后
- 用户查询成功后，预存款余额会立即被加载
- 无论用户切换到哪个标签页，余额都能正确显示

### 2. 标签页切换
- 切换到"余额退款"标签页时，会自动加载最新的预存款余额
- 确保余额数据的实时性

### 3. 数据一致性
- 充值标签页和退款标签页显示相同的余额数据
- 操作成功后余额会自动刷新

## 测试验证

### 测试步骤
1. 查询一个有预存款余额的用户
2. 切换到"余额退款"标签页
3. 检查"当前预存余额"是否正确显示

### 预期结果
- 余额应该显示正确的数值，而不是0元
- 余额应该与充值标签页显示的一致
- 退款金额输入框的最大值应该等于当前余额

## 代码变更总结

### 修改的文件
- `apps/web-antd/src/views/waterfee/bill/counter-payment/index.vue`

### 修改的函数
1. `handleTabChange` - 添加退款标签页的余额加载逻辑
2. `handleSelectUser` - 用户选择后立即加载余额
3. `handleSearch` - 用户查询后立即加载余额
4. `loadDepositBalance` - 优化错误处理

### 新增的调用点
- 标签页切换时的余额加载
- 用户查询成功后的余额初始化

## 注意事项

1. **性能考虑**: 余额加载是异步操作，不会阻塞用户界面
2. **错误处理**: 如果余额加载失败，会设置为0并记录错误日志
3. **用户体验**: 用户无需手动刷新，余额会自动更新

## 后续优化建议

1. 可以考虑添加余额加载的loading状态
2. 可以考虑缓存余额数据，减少重复请求
3. 可以考虑在余额变化时显示动画效果
