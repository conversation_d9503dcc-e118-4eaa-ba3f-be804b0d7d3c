<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import dayjs from 'dayjs';

import {
  DollarOutlined,
  PlusOutlined,
  ReloadOutlined,
  RollbackOutlined,
  SearchOutlined,
  UndoOutlined,
} from '@ant-design/icons-vue';
import {
  AutoComplete,
  Button,
  Card,
  DatePicker,
  Descriptions,
  DescriptionsItem,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Modal,
  RangePicker,
  Select,
  Switch,
  Table,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import {
  addDeposit,
  adjustConsumption,
  batchReversalPayment,
  calculateAdjustment,
  getUserBills,
  getUserById,
  getUserByKeyword,
  getUserPaymentDetails,
  // getUserMeterReadings,
  payBills,
  refundDeposit,
  reversalPayment,
  searchUsersByKeyword,
} from '#/api/waterfee/counter-payment';
import { billAdjustConsumption } from '#/api/waterfee/bill';
import { dictDataInfo } from '#/api/waterfee/public/dict-data';
import { getUserDepositBalance } from '#/api/waterfee/user-deposit';

// 使用原生Ant Design Vue组件
import {
  billColumns,
  billColumnsWithActions,
  paymentDetailColumns,
  paymentMethodOptions,
  refundMethodOptions,
} from './data';

// 搜索表单
const searchForm = reactive({
  keyword: '',
});

// 搜索选项
const searchOptions = ref<
  Array<{
    data: Record<string, any>;
    label: string;
    value: string;
  }>
>([]);

// 当前选中的用户ID（用于再次查询）
const selectedUserId = ref('');

// 账单月份过滤
const billMonthFilter = ref<string | undefined>(undefined);

// 用户信息
const userInfo = ref<{
  address: string;
  customerNature: string;
  phoneNumber: string;
  supplyDate: string;
  userId: string;
  userName: string;
  userNo: string;
  useWaterNature: string;
  useWaterNumber: number;
}>({
  userId: '',
  userNo: '',
  userName: '',
  phoneNumber: '',
  address: '',
  customerNature: '',
  useWaterNature: '',
  useWaterNumber: 0,
  supplyDate: '',
});

// 预存款余额
const depositBalance = ref(0);

// 账单列表
const billList = ref<Array<Record<string, any>>>([]);
const allBillList = ref<Array<Record<string, any>>>([]);
const paymentDetailList = ref<Array<Record<string, any>>>([]);
const meterReadingList = ref<Array<Record<string, any>>>([]);

// 选中的账单
const selectedBills = ref<Array<Record<string, any>>>([]);

// 当前激活的标签页
const activeTabKey = ref<string>('payment');

// 表格引用
const billTableRef = ref(null);
const paymentDetailTableRef = ref(null);
const allBillTableRef = ref(null);
const meterReadingTableRef = ref(null);

// 缴费弹窗
const paymentModalVisible = ref(false);
const paymentForm = reactive({
  amount: 0,
  paymentMethod: 'CASH',
  remark: '',
  tollCollector: '', 
});

// 预存充值表单
const depositForm = reactive({
  amount: 0,
  paymentMethod: 'CASH',
  remark: '',
  tollCollector: '', // 收费员
});

// 余额退款表单
const refundForm = reactive({
  refundAmount: 0,
  refundReason: '',
  refundMethod: 'CASH',
  bankCardNo: '',
  bankName: '',
  accountName: '',
  remark: '',
  forceRefund: false,
});

// 金额调整相关状态
const billAdjustModalVisible = ref(false);
const currentAdjustBill = ref(null);
const billAdjustForm = reactive({
  adjustmentsAmount: 0,
  adjustmentReason: '',
});

// 账单调整表单
const adjustmentForm = reactive({
  billId: undefined as number | undefined,
  // 用水量相关
  currentConsumption: 0,
  adjustedConsumption: 0,
  // 读数相关
  previousReadingValue: 0,
  currentReadingValue: 0,
  adjustedCurrentReadingValue: 0,
  // 费用相关
  baseChargeAmount: 0,
  adjustedBaseChargeAmount: 0,
  // 附加费相关
  additionalChargeAmount: 0,
  adjustedAdditionalChargeAmount: 0,
  // 违约金相关
  surchargeAmount: 0,
  adjustedSurchargeAmount: 0,
  // 总金额
  totalAmount: 0,
  adjustedTotalAmount: 0,
  // 阶梯水价相关
  priceType: 'STANDARD' as 'LADDER' | 'STANDARD', // 价格类型：'STANDARD'(标准价格), 'LADDER'(阶梯价格)
  // 阶梯1相关
  tier1: 0,
  tier1Amount: 0,
  adjustedTier1: 0,
  adjustedTier1Amount: 0,
  // 阶梯2相关
  tier2: 0,
  tier2Amount: 0,
  adjustedTier2: 0,
  adjustedTier2Amount: 0,
  // 阶梯3相关
  tier3: 0,
  tier3Amount: 0,
  adjustedTier3: 0,
  adjustedTier3Amount: 0,
  // 调整原因
  reason: '',
  // 调整类型：'consumption'(用量), 'reading'(读数), 'additionalCharge'(附加费), 'surcharge'(违约金)
  adjustmentType: 'consumption' as
    | 'additionalCharge'
    | 'consumption'
    | 'reading'
    | 'surcharge',
});

// 冲正表单
const reversalForm = reactive({
  paymentDetailId: '',
  paymentNo: '',
  userNo: '',
  userName: '',
  billMonth: '',
  originalAmount: 0,
  reversalReason: '',
  reversalType: 'MANUAL',
  forceReversal: false,
});

// 冲正时间范围过滤
const reversalTimeRange = ref([]);
const reversalStartDate = ref('');
const reversalEndDate = ref('');

// 快捷时间范围选择函数
function setTimeRange(type) {
  let startDate, endDate;

  switch (type) {
    case 'today':
      startDate = endDate = dayjs();
      break;
    case 'yesterday':
      startDate = endDate = dayjs().subtract(1, 'day');
      break;
    case 'week':
      startDate = dayjs().subtract(7, 'day');
      endDate = dayjs();
      break;
    case 'month':
      startDate = dayjs().startOf('month');
      endDate = dayjs();
      break;
    case 'lastMonth':
      startDate = dayjs().subtract(1, 'month').startOf('month');
      endDate = dayjs().subtract(1, 'month').endOf('month');
      break;
    default:
      return;
  }

  reversalTimeRange.value = [startDate, endDate];
  reversalStartDate.value = startDate.format('YYYY-MM-DD');
  reversalEndDate.value = endDate.format('YYYY-MM-DD');
}

// 计算属性：总缴费金额
const totalPaymentAmount = computed(() => {
  if (!selectedBills.value || selectedBills.value.length === 0) {
    return '0.00';
  }

  let total = 0;
  for (const bill of selectedBills.value) {
    const balanceDue = Number.parseFloat(bill.balanceDue || 0);
    if (!isNaN(balanceDue)) {
      total += balanceDue;
    }
  }

  return total.toFixed(2);
});

// 计算属性：可调整的账单选项
const adjustableBillOptions = computed(() => {
  return allBillList.value
    .filter(
      (bill) => bill.billStatus === 'DRAFT' || bill.billStatus === 'ISSUED',
    )
    .map((bill) => ({
      label: `${bill.billNumber} (${bill.billingPeriodStart?.slice(0, 10)} ~ ${bill.billingPeriodEnd?.slice(0, 10)})`,
      value: bill.billId,
    }));
});

// 计算属性：可冲正的缴费记录选项
const reversalPaymentOptions = computed(() => {
  if (!paymentDetailList.value) return [];

  let filteredPayments = paymentDetailList.value
    .filter((payment) => payment.paymentAmount > 0 && payment.paymentStatus !== 'REVERSED');

  // 时间范围过滤
  if (reversalStartDate.value && reversalEndDate.value) {
    const startDate = new Date(reversalStartDate.value);
    const endDate = new Date(reversalEndDate.value);
    // 设置结束日期为当天的23:59:59
    endDate.setHours(23, 59, 59, 999);

    filteredPayments = filteredPayments.filter((payment) => {
      if (!payment.paymentTime) return false;
      const paymentDate = new Date(payment.paymentTime);
      return paymentDate >= startDate && paymentDate <= endDate;
    });
  }

  return filteredPayments.map((payment) => ({
    label: `${payment.transactionId || payment.paymentDetailId || '缴费记录'} - ${payment.paymentTime} (金额: ${payment.paymentAmount}元)`,
    value: payment.paymentDetailId,
    data: payment,
  }));
});

// 计算属性：缴费表单是否有效
const isPaymentFormValid = computed(() => {
  return (
    paymentForm.amount >= 0 &&
    paymentForm.paymentMethod &&
    paymentForm.tollCollector &&
    paymentForm.tollCollector.trim() !== ''
  );
});

// 计算属性：调整表单是否有效
const isAdjustmentFormValid = computed(() => {
  // 必须有账单ID和调整原因
  if (!adjustmentForm.billId || !adjustmentForm.reason) {
    return false;
  }

  // 读数必须大于等于上期读数
  if (
    adjustmentForm.adjustedCurrentReadingValue <
    adjustmentForm.previousReadingValue
  ) {
    return false;
  }

  // 附加费和违约金必须大于等于0
  if (
    adjustmentForm.adjustedAdditionalChargeAmount < 0 ||
    adjustmentForm.adjustedSurchargeAmount < 0
  ) {
    return false;
  }

  // 至少有一项调整
  const hasReadingChange =
    adjustmentForm.adjustedCurrentReadingValue !==
    adjustmentForm.currentReadingValue;
  const hasAdditionalChargeChange =
    adjustmentForm.adjustedAdditionalChargeAmount !==
    adjustmentForm.additionalChargeAmount;
  const hasSurchargeChange =
    adjustmentForm.adjustedSurchargeAmount !== adjustmentForm.surchargeAmount;

  return hasReadingChange || hasAdditionalChargeChange || hasSurchargeChange;
});

// 计算属性：冲正表单是否有效
const isReversalFormValid = computed(() => {
  return (
    reversalForm.paymentDetailId &&
    reversalForm.reversalReason &&
    reversalForm.reversalReason.trim() !== ''
  );
});

// 处理搜索输入
async function handleSearchInput(value) {
  console.log('搜索输入:', value); // 调试日志
  if (!value || value.length < 2) {
    searchOptions.value = [];
    return;
  }

  try {
    // 调用模糊查询接口
    const res = await searchUsersByKeyword(value);
    console.log('搜索结果:', res); // 调试日志
    if (res && res.length > 0) {
      // 转换为AutoComplete需要的格式
      searchOptions.value = res.map((item) => ({
        value: `${item.userNo} - ${item.userName}`, // 使用用户友好的格式作为值
        label: `${item.userNo} - ${item.userName}`, // 显示用户编号和姓名
        data: {
          desc: item.phoneNumber || '无手机号码', // 显示手机号码作为描述
          userId: item.userId, // 保存用户ID用于后续查询
          ...item, // 保存完整的用户信息
        },
      }));
      console.log('转换后的搜索选项:', searchOptions.value); // 调试日志
    } else {
      searchOptions.value = [];
    }
  } catch (error) {
    console.error('模糊查询失败:', error);
    searchOptions.value = [];
  }
}

// 处理选择用户
async function handleSelectUser(value, option) {
  console.log('选择用户:', value, option); // 调试日志
  try {
    // 从option.data中获取用户ID
    const userId = option?.data?.userId;
    console.log('提取的用户ID:', userId); // 调试日志
    if (!userId) {
      message.warning('无法获取用户信息');
      return;
    }

    // 根据用户ID查询详细信息
    const res = await getUserById(userId);
    console.log('查询到的用户详情:', res); // 调试日志
    if (res && res.userId) {
      // 重置标签页为第一个（窗口收费）
      activeTabKey.value = 'payment';

      // 清空所有数据
      resetAllTabsData();

      // 设置用户信息
      userInfo.value = res;

      // 保存用户ID用于再次查询
      selectedUserId.value = userId;

      // 设置搜索框显示用户友好的文本（value已经是友好格式）
      searchForm.keyword = value;
      console.log('设置搜索框文本:', value); // 调试日志
      console.log('保存的用户ID:', userId); // 调试日志

      // 加载窗口收费标签页的数据
      loadPaymentData();

      // 同时加载预存款余额，以便退款标签页使用
      loadDepositBalance();
    } else {
      message.warning('未找到用户信息');
      resetUserData();
    }
  } catch (error) {
    console.error('查询用户详情失败:', error);
    message.error('查询用户详情失败，请稍后重试');
    resetUserData();
  }
}

// 搜索用户
async function handleSearch() {
  if (!searchForm.keyword) {
    message.warning('请输入用户编号或姓名');
    return;
  }

  try {
    let res;

    // 如果有保存的用户ID且搜索框内容是格式化的用户信息，优先使用ID查询
    if (selectedUserId.value && searchForm.keyword.includes(' - ')) {
      console.log('使用保存的用户ID查询:', selectedUserId.value); // 调试日志
      res = await getUserById(selectedUserId.value);
    } else {
      console.log('使用关键词查询:', searchForm.keyword); // 调试日志
      // 清空保存的用户ID，因为用户输入了新的关键词
      selectedUserId.value = '';
      res = await getUserByKeyword(searchForm.keyword);
    }

    if (res && res.userId) {
      // 重置标签页为第一个（窗口收费）
      activeTabKey.value = 'payment';

      // 清空所有数据
      resetAllTabsData();

      // 设置用户信息
      userInfo.value = res;

      // 保存用户ID
      selectedUserId.value = res.userId;

      // 加载窗口收费标签页的数据
      loadPaymentData();

      // 同时加载预存款余额，以便退款标签页使用
      loadDepositBalance();
    } else {
      message.warning('未找到用户信息');
      resetUserData();
    }
  } catch (error) {
    console.error('查询用户失败:', error);
    message.error('查询用户失败，请稍后重试');
    resetUserData();
  }
}

// 重置搜索
function resetSearch() {
  searchForm.keyword = '';
  searchOptions.value = [];
  selectedUserId.value = ''; // 清空保存的用户ID
  resetUserData();
}

// 清空所有标签页的数据
function resetAllTabsData() {
  // 清空窗口收费标签页数据
  billList.value = [];
  selectedBills.value = [];

  // 清空缴费明细标签页数据
  paymentDetailList.value = [];

  // 清空账单信息标签页数据
  allBillList.value = [];

  // 清空预存充值标签页数据
  depositBalance.value = 0;

  // 清空余额退款标签页数据
  refundForm.refundAmount = 0;
  refundForm.refundReason = '';
  refundForm.refundMethod = 'CASH';
  refundForm.bankCardNo = '';
  refundForm.bankName = '';
  refundForm.accountName = '';
  refundForm.remark = '';
  refundForm.forceRefund = false;

  // 清空用量调整标签页数据
  resetAdjustmentForm();

  // 清空缴费冲正标签页数据
  reversalForm.paymentDetailId = '';
  reversalForm.paymentNo = '';
  reversalForm.userNo = '';
  reversalForm.userName = '';
  reversalForm.billMonth = '';
  reversalForm.originalAmount = 0;
  reversalForm.reversalReason = '';
  reversalForm.reversalType = 'MANUAL';
  reversalForm.forceReversal = false;
  // 清空时间范围
  reversalTimeRange.value = [];
  reversalStartDate.value = '';
  reversalEndDate.value = '';

  // 清空其他数据
  meterReadingList.value = [];
}

// 重置用户数据
function resetUserData() {
  // 重置用户信息
  userInfo.value = {
    userId: '',
    userNo: '',
    userName: '',
    phoneNumber: '',
    address: '',
    customerNature: '',
    useWaterNature: '',
    useWaterNumber: 0,
    supplyDate: '',
  };

  // 清空保存的用户ID
  selectedUserId.value = '';

  // 重置标签页为第一个（窗口收费）
  activeTabKey.value = 'payment';

  // 清空所有标签页的数据
  resetAllTabsData();
}

// 加载用户相关数据
async function loadUserData(_userId: string) {
  try {
    // 确保用户信息已加载
    if (!userInfo.value.userId) {
      return;
    }

    // 根据当前激活的标签页加载相应的数据
    switch (activeTabKey.value) {
      case 'adjustment': {
        // 用量调整：加载账单数据（用于选择可调整的账单）
        loadBillInfoData();

        break;
      }
      case 'billInfo': {
        // 账单信息：加载账单数据
        loadBillInfoData();

        break;
      }
      case 'deposit': {
        // 预存充值：加载预存款余额
        loadDepositBalance();

        break;
      }
      case 'refund': {
        // 余额退款：加载预存款余额
        loadDepositBalance();

        break;
      }
      case 'payment': {
        // 窗口收费：加载待缴费账单
        loadPaymentData();

        break;
      }
      case 'paymentDetail': {
        // 缴费明细：加载缴费明细数据
        loadPaymentDetailData();

        break;
      }
      case 'reversal': {
        // 缴费冲正：加载缴费明细数据（用于选择可冲正的缴费记录）
        loadPaymentDetailData();

        break;
      }
      default: {
        // 默认加载窗口收费数据
        loadPaymentData();
      }
    }
  } catch (error) {
    console.error('加载用户数据失败:', error);
    message.error('加载用户数据失败，请稍后重试');
  }
}

// 处理标签页切换
function handleTabChange(key: string) {
  activeTabKey.value = key;

  // 如果用户已经加载
  if (userInfo.value.userId) {
    console.log('切换到标签页:', key);

    // 根据不同的标签页加载相应的数据
    switch (key) {
      case 'adjustment': {
        // 用量调整：加载账单数据（用于选择可调整的账单）
        console.log('加载用量调整数据');
        // 确保账单数据已加载
        if (!allBillList.value || allBillList.value.length === 0) {
          loadBillInfoData();
        }

        break;
      }
      case 'billInfo': {
        // 账单信息：加载账单数据
        loadBillInfoData();

        break;
      }
      case 'deposit': {
        // 预存充值：加载预存款余额
        loadDepositBalance();

        break;
      }
      case 'refund': {
        // 余额退款：加载预存款余额
        loadDepositBalance();

        break;
      }
      case 'payment': {
        // 窗口收费：加载待缴费账单
        loadPaymentData();

        break;
      }
      case 'paymentDetail': {
        // 缴费明细：加载缴费明细数据
        loadPaymentDetailData();

        break;
      }
      case 'reversal': {
        // 缴费冲正：加载缴费明细数据（用于选择可冲正的缴费记录）
        // 确保缴费明细数据已加载
        if (!paymentDetailList.value || paymentDetailList.value.length === 0) {
          loadPaymentDetailData();
        }

        break;
      }
      // No default
    }
  } else {
    console.warn('用户信息未加载，无法加载标签页数据');
  }
}

// 处理账单月份变更
async function handleBillMonthChange(_: unknown, _dateString: string) {
  if (userInfo.value.userId) {
    loadBillInfoData();
  }
}

// 加载窗口收费数据（待缴费账单）
async function loadPaymentData() {
  try {
    // 加载待缴费账单
    const billsRes = await getUserBills(userInfo.value.userId.toString(), {
      status: 'ISSUED',
    });
    billList.value = billsRes || [];
  } catch (error) {
    console.error('加载待缴费账单失败:', error);
    message.error('加载待缴费账单失败，请稍后重试');
  }
}

// 加载账单信息数据
async function loadBillInfoData() {
  try {
    // 构建查询参数
    const params: { billMonth?: string } = {};
    if (billMonthFilter.value) {
      params.billMonth = billMonthFilter.value;
    }

    // 加载账单数据
    const allBillsRes = await getUserBills(
      userInfo.value.userId.toString(),
      params,
    );
    allBillList.value = allBillsRes || [];

    // 调试日志：查看账单数据和可调整的账单选项
    console.log('账单数据:', allBillList.value);
    console.log('可调整的账单选项:', adjustableBillOptions.value);
  } catch (error) {
    console.error('加载账单数据失败:', error);
    message.error('加载账单数据失败，请稍后重试');
  }
}

// 加载缴费明细数据
async function loadPaymentDetailData() {
  try {
    // 加载缴费明细
    const paymentDetailsRes = await getUserPaymentDetails(
      userInfo.value.userId,
    );
    console.log('加载的缴费明细数据:', paymentDetailsRes); // 调试日志
    paymentDetailList.value = paymentDetailsRes || [];
    console.log('设置后的缴费明细列表:', paymentDetailList.value); // 调试日志
  } catch (error) {
    console.error('加载缴费明细失败:', error);
    message.error('加载缴费明细失败，请稍后重试');
  }
}

// 加载预存款余额
async function loadDepositBalance() {
  if (userInfo.value.userId) {
    try {
      const balanceRes = await getUserDepositBalance(userInfo.value.userId);
      depositBalance.value = balanceRes || 0;
    } catch (error) {
      console.error('加载预存款余额失败:', error);
      depositBalance.value = 0;
    }
  }
}

// 处理账单选择变化
function handleBillSelectionChange(
  _selectedRowKeys: unknown[],
  selectedRows: Record<string, any>[],
) {
  selectedBills.value = selectedRows;
}

// 处理缴费
function handlePayBills() {
  if (selectedBills.value.length === 0) {
    message.warning('请选择需要缴费的账单');
    return;
  }

  // 设置默认缴费金额为总应付金额
  const amount = Number.parseFloat(totalPaymentAmount.value);
  paymentForm.amount = isNaN(amount) ? 0 : amount;
  paymentForm.paymentMethod = 'CASH';
  paymentForm.remark = '';
  paymentForm.tollCollector = ''; // 重置收费员字段

  // 显示缴费弹窗
  paymentModalVisible.value = true;
}

// 取消缴费
function cancelPayment() {
  paymentModalVisible.value = false;
}

// 确认缴费
async function confirmPayment() {
  // 金额为 null、undefined、负数校验
  if (paymentForm.amount === null || paymentForm.amount === undefined || paymentForm.amount < 0) {
    message.warning('请输入有效的缴费金额');
    return;
  }

  // 实缴金额不得小于应缴总金额
  if (paymentForm.amount < totalPaymentAmount.value) {
    message.warning(`实缴金额不能小于应缴总金额 ${totalPaymentAmount.value} 元`);
    return;
  }

  if (!paymentForm.paymentMethod) {
    message.warning('请选择支付方式');
    return;
  }

  if (!paymentForm.tollCollector || paymentForm.tollCollector.trim() === '') {
    message.warning('请输入收费员姓名');
    return;
  }

  if (!isPaymentFormValid.value) {
    message.warning('请填写完整的缴费信息');
    return;
  }

  try {
    const billIds = selectedBills.value.map((bill) => bill.billId);
    await payBills({
      billIds,
      amount: paymentForm.amount,
      paymentMethod: paymentForm.paymentMethod,
      remark: paymentForm.remark,
      tollCollector: paymentForm.tollCollector,
    });

    message.success('缴费成功');
    paymentModalVisible.value = false;

    // 清空选中的账单和表单数据
    selectedBills.value = [];
    paymentForm.amount = 0;
    paymentForm.paymentMethod = 'CASH';
    paymentForm.remark = '';
    paymentForm.tollCollector = '';

    // 重新加载相关数据
    if (userInfo.value.userId) {
      // 重新加载窗口收费数据（账单列表）
      loadPaymentData();

      // 重新加载账单信息数据（如果在账单信息标签页）
      if (activeTabKey.value === 'billInfo') {
        loadBillInfoData();
      }

      // 重新加载缴费明细数据（如果在缴费明细标签页）
      if (activeTabKey.value === 'paymentDetail') {
        loadPaymentDetailData();
      }

      // 重新加载预存款余额（影响充值和退款标签页）
      loadDepositBalance();
    }
  } catch (error) {
    console.error('缴费失败:', error);
    message.error('缴费失败，请稍后重试');
  }
}


// 处理预存充值
async function handleDeposit() {
  // 详细验证必填项
  if (!depositForm.amount || depositForm.amount <= 0) {
    message.warning('请输入有效的充值金额');
    return;
  }

  if (!depositForm.paymentMethod) {
    message.warning('请选择支付方式');
    return;
  }

  if (!depositForm.tollCollector || depositForm.tollCollector.trim() === '') {
    message.warning('请输入收费员姓名');
    return;
  }

  try {
    await addDeposit({
      userId: userInfo.value.userId,
      amount: depositForm.amount,
      paymentMethod: depositForm.paymentMethod,
      remark: depositForm.remark,
      tollCollector: depositForm.tollCollector,
    });

    message.success('充值成功');

    // 重置表单
    depositForm.amount = 0;
    depositForm.remark = '';
    depositForm.tollCollector = ''; // 重置收费员字段

    // 重新加载相关数据
    if (userInfo.value.userId) {
      // 重新加载预存款余额
      loadDepositBalance();

      // 重新加载缴费明细数据（如果在缴费明细标签页）
      if (activeTabKey.value === 'paymentDetail') {
        loadPaymentDetailData();
      }
    }
  } catch (error) {
    console.error('充值失败:', error);
    message.error('充值失败，请稍后重试');
  }
}

// 处理余额退款
async function handleRefund() {
  // 详细验证必填项
  if (!refundForm.refundAmount || refundForm.refundAmount <= 0) {
    message.warning('请输入有效的退款金额');
    return;
  }

  if (refundForm.refundAmount > depositBalance.value) {
    message.warning('退款金额不能超过当前余额');
    return;
  }

  if (!refundForm.refundReason || refundForm.refundReason.trim() === '') {
    message.warning('请输入退款原因');
    return;
  }

  if (!refundForm.refundMethod) {
    message.warning('请选择退款方式');
    return;
  }

  // 银行卡退款时的额外验证
  if (refundForm.refundMethod === 'BANK_CARD') {
    if (!refundForm.bankCardNo || refundForm.bankCardNo.trim() === '') {
      message.warning('银行卡退款时，银行卡号不能为空');
      return;
    }
    if (!refundForm.bankName || refundForm.bankName.trim() === '') {
      message.warning('银行卡退款时，开户行不能为空');
      return;
    }
    if (!refundForm.accountName || refundForm.accountName.trim() === '') {
      message.warning('银行卡退款时，账户名不能为空');
      return;
    }
  }

  try {
    await refundDeposit({
      userId: userInfo.value.userId,
      refundAmount: refundForm.refundAmount,
      refundReason: refundForm.refundReason,
      refundMethod: refundForm.refundMethod,
      bankCardNo: refundForm.bankCardNo,
      bankName: refundForm.bankName,
      accountName: refundForm.accountName,
      remark: refundForm.remark,
      forceRefund: refundForm.forceRefund,
    });

    message.success('退款成功');

    // 重置表单
    refundForm.refundAmount = 0;
    refundForm.refundReason = '';
    refundForm.refundMethod = 'CASH';
    refundForm.bankCardNo = '';
    refundForm.bankName = '';
    refundForm.accountName = '';
    refundForm.remark = '';
    refundForm.forceRefund = false;

    // 重新加载相关数据
    if (userInfo.value.userId) {
      // 重新加载预存款余额
      loadDepositBalance();

      // 重新加载缴费明细数据（如果在缴费明细标签页）
      if (activeTabKey.value === 'paymentDetail') {
        loadPaymentDetailData();
      }
    }
  } catch (error) {
    console.error('退款失败:', error);
    message.error('退款失败，请稍后重试');
  }
}

// 打开金额调整弹窗
function openBillAdjustModal(record) {
  currentAdjustBill.value = record;
  billAdjustForm.adjustmentsAmount = record.adjustmentsAmount || 0;
  billAdjustForm.adjustmentReason = '';
  billAdjustModalVisible.value = true;
}

// 确认金额调整
async function confirmBillAdjust() {
  if (!billAdjustForm.adjustmentReason || billAdjustForm.adjustmentReason.trim() === '') {
    message.warning('请输入调整原因');
    return;
  }

  try {
    await billAdjustConsumption({
      billId: currentAdjustBill.value.billId,
      adjustmentsAmount: Number(billAdjustForm.adjustmentsAmount),
      adjustmentReason: billAdjustForm.adjustmentReason,
    });

    message.success('金额调整成功');
    billAdjustModalVisible.value = false;

    // 重新加载相关数据
    if (userInfo.value.userId) {
      // 重新加载窗口收费数据（账单列表）
      loadPaymentData();

      // 重新加载账单信息数据（如果在账单信息标签页）
      if (activeTabKey.value === 'billInfo') {
        loadBillInfoData();
      }
    }
  } catch (error) {
    console.error('金额调整失败:', error);
    message.error('金额调整失败，请稍后重试');
  }
}

// 取消金额调整
function cancelBillAdjust() {
  billAdjustModalVisible.value = false;
  billAdjustForm.adjustmentsAmount = 0;
  billAdjustForm.adjustmentReason = '';
  currentAdjustBill.value = null;
}

// 处理调整账单选择变化
async function handleAdjustmentBillChange(billId: any) {
  console.log('选择调整账单:', billId);

  // 检查账单列表是否已加载
  if (!allBillList.value || allBillList.value.length === 0) {
    console.warn('账单数据未加载，正在加载数据...');
    message.warning('正在加载账单数据，请稍后再试');

    // 尝试加载账单数据
    if (userInfo.value.userId) {
      try {
        await loadBillInfoData();
        // 数据加载完成后重新尝试选择账单
        if (allBillList.value && allBillList.value.length > 0) {
          handleAdjustmentBillChange(billId);
        }
      } catch (error) {
        console.error('加载账单数据失败:', error);
        message.error('加载账单数据失败，请稍后重试');
      }
      return;
    }
    return;
  }

  const selectedBill = allBillList.value.find((bill) => bill.billId === billId);
  console.log('找到的账单:', selectedBill);

  if (selectedBill) {
    // 设置账单ID
    adjustmentForm.billId = Number(billId);

    try {
      // 调用后端计算接口获取初始数据
      // 创建一个简单的请求对象
      const requestData: Record<string, any> = {
        billId: Number(billId), // 确保 billId 是数字
      };
      const response = await calculateAdjustment(requestData);

      if (response) {
        const result = response;
        console.log('计算调整结果:', result);

        // 设置用水量相关数据
        adjustmentForm.currentConsumption =
          Number.parseFloat(result.originalConsumption) || 0;
        adjustmentForm.adjustedConsumption =
          Number.parseFloat(result.adjustedConsumption) || 0;

        // 设置读数相关数据
        adjustmentForm.previousReadingValue =
          Number.parseFloat(result.originalPreviousReading) || 0;
        adjustmentForm.currentReadingValue =
          Number.parseFloat(result.originalCurrentReading) || 0;
        adjustmentForm.adjustedCurrentReadingValue =
          Number.parseFloat(result.adjustedCurrentReading) || 0;

        // 设置费用相关数据
        adjustmentForm.baseChargeAmount =
          Number.parseFloat(result.originalBaseCharge) || 0;
        adjustmentForm.adjustedBaseChargeAmount =
          Number.parseFloat(result.adjustedBaseCharge) || 0;

        // 设置附加费相关数据
        adjustmentForm.additionalChargeAmount =
          Number.parseFloat(result.originalAdditionalCharge) || 0;
        adjustmentForm.adjustedAdditionalChargeAmount =
          Number.parseFloat(result.adjustedAdditionalCharge) || 0;

        // 设置违约金相关数据
        adjustmentForm.surchargeAmount =
          Number.parseFloat(result.originalSurcharge) || 0;
        adjustmentForm.adjustedSurchargeAmount =
          Number.parseFloat(result.adjustedSurcharge) || 0;

        // 设置总金额
        adjustmentForm.totalAmount =
          Number.parseFloat(result.originalTotalAmount) || 0;
        adjustmentForm.adjustedTotalAmount =
          Number.parseFloat(result.adjustedTotalAmount) || 0;

        // 设置价格类型
        adjustmentForm.priceType = result.priceType || 'STANDARD';

        // 如果是阶梯水价，设置阶梯水价相关数据
        if (adjustmentForm.priceType === 'LADDER') {
          // 设置阶梯1相关数据
          adjustmentForm.tier1 = Number.parseFloat(result.originalTier1) || 0;
          adjustmentForm.tier1Amount =
            Number.parseFloat(result.originalTier1Amount) || 0;
          adjustmentForm.adjustedTier1 =
            Number.parseFloat(result.adjustedTier1) || 0;
          adjustmentForm.adjustedTier1Amount =
            Number.parseFloat(result.adjustedTier1Amount) || 0;

          // 设置阶梯2相关数据
          adjustmentForm.tier2 = Number.parseFloat(result.originalTier2) || 0;
          adjustmentForm.tier2Amount =
            Number.parseFloat(result.originalTier2Amount) || 0;
          adjustmentForm.adjustedTier2 =
            Number.parseFloat(result.adjustedTier2) || 0;
          adjustmentForm.adjustedTier2Amount =
            Number.parseFloat(result.adjustedTier2Amount) || 0;

          // 设置阶梯3相关数据
          adjustmentForm.tier3 = Number.parseFloat(result.originalTier3) || 0;
          adjustmentForm.tier3Amount =
            Number.parseFloat(result.originalTier3Amount) || 0;
          adjustmentForm.adjustedTier3 =
            Number.parseFloat(result.adjustedTier3) || 0;
          adjustmentForm.adjustedTier3Amount =
            Number.parseFloat(result.adjustedTier3Amount) || 0;
        }
      } else {
        // 如果后端计算接口没有返回数据，使用账单数据
        // 设置用水量相关数据
        adjustmentForm.currentConsumption = selectedBill.consumptionVolume || 0;
        adjustmentForm.adjustedConsumption =
          selectedBill.consumptionVolume || 0;

        // 设置读数相关数据
        adjustmentForm.previousReadingValue =
          selectedBill.previousReadingValue || 0;
        adjustmentForm.currentReadingValue =
          selectedBill.currentReadingValue || 0;
        adjustmentForm.adjustedCurrentReadingValue =
          selectedBill.currentReadingValue || 0;

        // 设置费用相关数据
        adjustmentForm.baseChargeAmount = selectedBill.baseChargeAmount || 0;
        adjustmentForm.adjustedBaseChargeAmount =
          selectedBill.baseChargeAmount || 0;

        // 设置附加费相关数据
        adjustmentForm.additionalChargeAmount =
          selectedBill.additionalChargeAmount || 0;
        adjustmentForm.adjustedAdditionalChargeAmount =
          selectedBill.additionalChargeAmount || 0;

        // 设置违约金相关数据
        adjustmentForm.surchargeAmount = selectedBill.surchargeAmount || 0;
        adjustmentForm.adjustedSurchargeAmount =
          selectedBill.surchargeAmount || 0;

        // 设置总金额
        adjustmentForm.totalAmount = selectedBill.totalAmount || 0;
        adjustmentForm.adjustedTotalAmount = selectedBill.totalAmount || 0;
      }
    } catch (error) {
      console.error('获取账单初始数据失败:', error);

      // 如果后端计算接口调用失败，使用账单数据
      // 设置用水量相关数据
      adjustmentForm.currentConsumption = selectedBill.consumptionVolume || 0;
      adjustmentForm.adjustedConsumption = selectedBill.consumptionVolume || 0;

      // 设置读数相关数据
      adjustmentForm.previousReadingValue =
        selectedBill.previousReadingValue || 0;
      adjustmentForm.currentReadingValue =
        selectedBill.currentReadingValue || 0;
      adjustmentForm.adjustedCurrentReadingValue =
        selectedBill.currentReadingValue || 0;

      // 设置费用相关数据
      adjustmentForm.baseChargeAmount = selectedBill.baseChargeAmount || 0;
      adjustmentForm.adjustedBaseChargeAmount =
        selectedBill.baseChargeAmount || 0;

      // 设置附加费相关数据
      adjustmentForm.additionalChargeAmount =
        selectedBill.additionalChargeAmount || 0;
      adjustmentForm.adjustedAdditionalChargeAmount =
        selectedBill.additionalChargeAmount || 0;

      // 设置违约金相关数据
      adjustmentForm.surchargeAmount = selectedBill.surchargeAmount || 0;
      adjustmentForm.adjustedSurchargeAmount =
        selectedBill.surchargeAmount || 0;

      // 设置总金额
      adjustmentForm.totalAmount = selectedBill.totalAmount || 0;
      adjustmentForm.adjustedTotalAmount = selectedBill.totalAmount || 0;
    }

    // 清空调整原因
    adjustmentForm.reason = '';

    console.log('设置调整表单:', adjustmentForm);
  } else {
    console.warn('未找到对应的账单数据');
    // 重置表单
    resetAdjustmentForm();
  }
}

// 重置调整表单
function resetAdjustmentForm() {
  adjustmentForm.billId = undefined;
  adjustmentForm.currentConsumption = 0;
  adjustmentForm.adjustedConsumption = 0;
  adjustmentForm.previousReadingValue = 0;
  adjustmentForm.currentReadingValue = 0;
  adjustmentForm.adjustedCurrentReadingValue = 0;
  adjustmentForm.baseChargeAmount = 0;
  adjustmentForm.adjustedBaseChargeAmount = 0;
  adjustmentForm.additionalChargeAmount = 0;
  adjustmentForm.adjustedAdditionalChargeAmount = 0;
  adjustmentForm.surchargeAmount = 0;
  adjustmentForm.adjustedSurchargeAmount = 0;
  adjustmentForm.totalAmount = 0;
  adjustmentForm.adjustedTotalAmount = 0;
  // 重置阶梯水价相关字段
  adjustmentForm.priceType = 'STANDARD';
  adjustmentForm.tier1 = 0;
  adjustmentForm.tier1Amount = 0;
  adjustmentForm.adjustedTier1 = 0;
  adjustmentForm.adjustedTier1Amount = 0;
  adjustmentForm.tier2 = 0;
  adjustmentForm.tier2Amount = 0;
  adjustmentForm.adjustedTier2 = 0;
  adjustmentForm.adjustedTier2Amount = 0;
  adjustmentForm.tier3 = 0;
  adjustmentForm.tier3Amount = 0;
  adjustmentForm.adjustedTier3 = 0;
  adjustmentForm.adjustedTier3Amount = 0;
  adjustmentForm.reason = '';
  adjustmentForm.adjustmentType = 'consumption';
}

// 处理读数变更，调用后端计算
async function handleReadingChange(value: number) {
  const previousReadingValue = Number(adjustmentForm.previousReadingValue) || 0;
  const currentReadingValue = Number(adjustmentForm.currentReadingValue) || 0;

  if (value < previousReadingValue) {
    message.warning('调整后读数不能小于上期读数');
    adjustmentForm.adjustedCurrentReadingValue = currentReadingValue;
    return;
  }

  // 调用后端计算接口
  try {
    // 创建一个简单的请求对象，包含所有可能的调整项
    const requestData: Record<string, any> = {
      billId: Number(adjustmentForm.billId), // 确保 billId 是数字
      adjustmentReading: value,
      adjustmentAdditionalCharge: adjustmentForm.adjustedAdditionalChargeAmount,
      adjustmentSurcharge: adjustmentForm.adjustedSurchargeAmount,
    };
    console.log('读数调整请求参数:', requestData);
    const response = await calculateAdjustment(requestData);
    console.log('读数调整计算结果:', response);
    if (response) {
      const result = response;
      console.log('读数调整计算结果:', result);
      console.log(
        '读数调整计算结果 - adjustedConsumption:',
        result.adjustedConsumption,
      );
      console.log(
        '读数调整计算结果 - adjustedBaseCharge:',
        result.adjustedBaseCharge,
      );
      console.log(
        '读数调整计算结果 - adjustedTotalAmount:',
        result.adjustedTotalAmount,
      );

      // 更新表单数据
      console.log('更新前的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
      });

      // 创建一个临时对象，用于一次性更新所有字段
      const tempForm = {
        // 更新调整后用水量（红色箭头指向的第一个字段）
        adjustedConsumption: Number.parseFloat(result.adjustedConsumption) || 0,
        // 更新调整后基础费用（红色箭头指向的第二个字段）
        adjustedBaseChargeAmount:
          Number.parseFloat(result.adjustedBaseCharge) || 0,
        // 保持附加费的值不变，因为这是用户输入的
        adjustedAdditionalChargeAmount: isNaN(
          Number.parseFloat(result.adjustedAdditionalCharge),
        )
          ? adjustmentForm.adjustedAdditionalChargeAmount
          : Number.parseFloat(result.adjustedAdditionalCharge),
        // 保持违约金的值不变，因为这是用户输入的
        adjustedSurchargeAmount: isNaN(
          Number.parseFloat(result.adjustedSurcharge),
        )
          ? adjustmentForm.adjustedSurchargeAmount
          : Number.parseFloat(result.adjustedSurcharge),
        // 更新调整后总金额（红色箭头指向的第三个字段）
        adjustedTotalAmount: Number.parseFloat(result.adjustedTotalAmount) || 0,
      };

      console.log('新的调整后用水量:', tempForm.adjustedConsumption);
      console.log('新的调整后基础费用:', tempForm.adjustedBaseChargeAmount);
      console.log('新的调整后总金额:', tempForm.adjustedTotalAmount);

      // 一次性更新所有字段
      Object.assign(adjustmentForm, tempForm);

      // 使用nextTick确保Vue能够正确更新视图
      nextTick(() => {
        console.log('视图更新后的表单数据:', {
          adjustedConsumption: adjustmentForm.adjustedConsumption,
          adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
          adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        });
      });

      console.log('更新后的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
      });

      // 如果是阶梯水价，更新阶梯水价相关数据
      if (adjustmentForm.priceType === 'LADDER') {
        // 更新阶梯1相关数据
        adjustmentForm.adjustedTier1 =
          Number.parseFloat(result.adjustedTier1) || 0;
        adjustmentForm.adjustedTier1Amount =
          Number.parseFloat(result.adjustedTier1Amount) || 0;

        // 更新阶梯2相关数据
        adjustmentForm.adjustedTier2 =
          Number.parseFloat(result.adjustedTier2) || 0;
        adjustmentForm.adjustedTier2Amount =
          Number.parseFloat(result.adjustedTier2Amount) || 0;

        // 更新阶梯3相关数据
        adjustmentForm.adjustedTier3 =
          Number.parseFloat(result.adjustedTier3) || 0;
        adjustmentForm.adjustedTier3Amount =
          Number.parseFloat(result.adjustedTier3Amount) || 0;
      }

      console.log('读数调整后的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        priceType: adjustmentForm.priceType,
        ...(adjustmentForm.priceType === 'LADDER'
          ? {
              adjustedTier1: adjustmentForm.adjustedTier1,
              adjustedTier1Amount: adjustmentForm.adjustedTier1Amount,
              adjustedTier2: adjustmentForm.adjustedTier2,
              adjustedTier2Amount: adjustmentForm.adjustedTier2Amount,
              adjustedTier3: adjustmentForm.adjustedTier3,
              adjustedTier3Amount: adjustmentForm.adjustedTier3Amount,
            }
          : {}),
      });
    }
  } catch (error: any) {
    console.error('计算调整结果失败:', error);
    message.error(`计算调整结果失败: ${error.message || '请稍后重试'}`);

    // 恢复原值
    adjustmentForm.adjustedCurrentReadingValue = currentReadingValue;
  }
}

// 注意：不再需要处理用量变更，因为用量是根据读数自动计算的

// 处理附加费变更，调用后端计算
async function handleAdditionalChargeChange(value: number) {
  const additionalChargeAmount =
    Number(adjustmentForm.additionalChargeAmount) || 0;

  if (value < 0) {
    message.warning('调整后附加费不能小于0');
    adjustmentForm.adjustedAdditionalChargeAmount = 0;
    return;
  }

  // 调用后端计算接口
  try {
    // 创建一个简单的请求对象，包含所有可能的调整项
    const requestData: Record<string, any> = {
      billId: Number(adjustmentForm.billId), // 确保 billId 是数字
      adjustmentReading: adjustmentForm.adjustedCurrentReadingValue,
      adjustmentAdditionalCharge: value,
      adjustmentSurcharge: adjustmentForm.adjustedSurchargeAmount,
    };
    const response = await calculateAdjustment(requestData);

    if (response) {
      const result = response;
      console.log('附加费调整计算结果:', result);

      // 更新表单数据
      console.log('更新前的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
      });

      // 创建一个临时对象，用于一次性更新所有字段
      const tempForm = {
        // 更新调整后用水量（红色箭头指向的第一个字段）
        adjustedConsumption: Number.parseFloat(result.adjustedConsumption) || 0,
        // 更新调整后基础费用（红色箭头指向的第二个字段）
        adjustedBaseChargeAmount:
          Number.parseFloat(result.adjustedBaseCharge) || 0,
        // 保持附加费的值为用户输入的值
        adjustedAdditionalChargeAmount: value,
        // 保持违约金的值不变
        adjustedSurchargeAmount: isNaN(
          Number.parseFloat(result.adjustedSurcharge),
        )
          ? adjustmentForm.adjustedSurchargeAmount
          : Number.parseFloat(result.adjustedSurcharge),
        // 更新调整后总金额（红色箭头指向的第三个字段）
        adjustedTotalAmount: Number.parseFloat(result.adjustedTotalAmount) || 0,
      };

      console.log('新的调整后用水量:', tempForm.adjustedConsumption);
      console.log('新的调整后基础费用:', tempForm.adjustedBaseChargeAmount);
      console.log('新的调整后总金额:', tempForm.adjustedTotalAmount);

      // 一次性更新所有字段
      Object.assign(adjustmentForm, tempForm);

      // 使用nextTick确保Vue能够正确更新视图
      nextTick(() => {
        console.log('视图更新后的表单数据:', {
          adjustedConsumption: adjustmentForm.adjustedConsumption,
          adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
          adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        });
      });

      // 如果是阶梯水价，更新阶梯水价相关数据
      if (adjustmentForm.priceType === 'LADDER') {
        // 更新阶梯1相关数据
        adjustmentForm.adjustedTier1 =
          Number.parseFloat(result.adjustedTier1) || 0;
        adjustmentForm.adjustedTier1Amount =
          Number.parseFloat(result.adjustedTier1Amount) || 0;

        // 更新阶梯2相关数据
        adjustmentForm.adjustedTier2 =
          Number.parseFloat(result.adjustedTier2) || 0;
        adjustmentForm.adjustedTier2Amount =
          Number.parseFloat(result.adjustedTier2Amount) || 0;

        // 更新阶梯3相关数据
        adjustmentForm.adjustedTier3 =
          Number.parseFloat(result.adjustedTier3) || 0;
        adjustmentForm.adjustedTier3Amount =
          Number.parseFloat(result.adjustedTier3Amount) || 0;
      }

      console.log('附加费调整后的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        priceType: adjustmentForm.priceType,
        ...(adjustmentForm.priceType === 'LADDER'
          ? {
              adjustedTier1: adjustmentForm.adjustedTier1,
              adjustedTier1Amount: adjustmentForm.adjustedTier1Amount,
              adjustedTier2: adjustmentForm.adjustedTier2,
              adjustedTier2Amount: adjustmentForm.adjustedTier2Amount,
              adjustedTier3: adjustmentForm.adjustedTier3,
              adjustedTier3Amount: adjustmentForm.adjustedTier3Amount,
            }
          : {}),
      });
    }
  } catch (error: any) {
    console.error('计算调整结果失败:', error);
    message.error(`计算调整结果失败: ${error.message || '请稍后重试'}`);

    // 恢复原值
    adjustmentForm.adjustedAdditionalChargeAmount = additionalChargeAmount;
  }
}

// 处理违约金变更，调用后端计算
async function handleSurchargeChange(value: number) {
  const surchargeAmount = Number(adjustmentForm.surchargeAmount) || 0;

  if (value < 0) {
    message.warning('调整后违约金不能小于0');
    adjustmentForm.adjustedSurchargeAmount = 0;
    return;
  }

  // 调用后端计算接口
  try {
    // 创建一个简单的请求对象，包含所有可能的调整项
    const requestData: Record<string, any> = {
      billId: Number(adjustmentForm.billId), // 确保 billId 是数字
      adjustmentReading: adjustmentForm.adjustedCurrentReadingValue,
      adjustmentAdditionalCharge: adjustmentForm.adjustedAdditionalChargeAmount,
      adjustmentSurcharge: value,
    };
    const response = await calculateAdjustment(requestData);

    if (response) {
      const result = response;
      console.log('违约金调整计算结果:', result);

      // 更新表单数据
      console.log('更新前的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
      });

      // 创建一个临时对象，用于一次性更新所有字段
      const tempForm = {
        // 更新调整后用水量（红色箭头指向的第一个字段）
        adjustedConsumption: Number.parseFloat(result.adjustedConsumption) || 0,
        // 更新调整后基础费用（红色箭头指向的第二个字段）
        adjustedBaseChargeAmount:
          Number.parseFloat(result.adjustedBaseCharge) || 0,
        // 保持附加费的值不变
        adjustedAdditionalChargeAmount: isNaN(
          Number.parseFloat(result.adjustedAdditionalCharge),
        )
          ? adjustmentForm.adjustedAdditionalChargeAmount
          : Number.parseFloat(result.adjustedAdditionalCharge),
        // 保持违约金的值为用户输入的值
        adjustedSurchargeAmount: value,
        // 更新调整后总金额（红色箭头指向的第三个字段）
        adjustedTotalAmount: Number.parseFloat(result.adjustedTotalAmount) || 0,
      };

      console.log('新的调整后用水量:', tempForm.adjustedConsumption);
      console.log('新的调整后基础费用:', tempForm.adjustedBaseChargeAmount);
      console.log('新的调整后总金额:', tempForm.adjustedTotalAmount);

      // 一次性更新所有字段
      Object.assign(adjustmentForm, tempForm);

      // 使用nextTick确保Vue能够正确更新视图
      nextTick(() => {
        console.log('视图更新后的表单数据:', {
          adjustedConsumption: adjustmentForm.adjustedConsumption,
          adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
          adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        });
      });

      // 如果是阶梯水价，更新阶梯水价相关数据
      if (adjustmentForm.priceType === 'LADDER') {
        // 更新阶梯1相关数据
        adjustmentForm.adjustedTier1 =
          Number.parseFloat(result.adjustedTier1) || 0;
        adjustmentForm.adjustedTier1Amount =
          Number.parseFloat(result.adjustedTier1Amount) || 0;

        // 更新阶梯2相关数据
        adjustmentForm.adjustedTier2 =
          Number.parseFloat(result.adjustedTier2) || 0;
        adjustmentForm.adjustedTier2Amount =
          Number.parseFloat(result.adjustedTier2Amount) || 0;

        // 更新阶梯3相关数据
        adjustmentForm.adjustedTier3 =
          Number.parseFloat(result.adjustedTier3) || 0;
        adjustmentForm.adjustedTier3Amount =
          Number.parseFloat(result.adjustedTier3Amount) || 0;
      }

      console.log('违约金调整后的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        priceType: adjustmentForm.priceType,
        ...(adjustmentForm.priceType === 'LADDER'
          ? {
              adjustedTier1: adjustmentForm.adjustedTier1,
              adjustedTier1Amount: adjustmentForm.adjustedTier1Amount,
              adjustedTier2: adjustmentForm.adjustedTier2,
              adjustedTier2Amount: adjustmentForm.adjustedTier2Amount,
              adjustedTier3: adjustmentForm.adjustedTier3,
              adjustedTier3Amount: adjustmentForm.adjustedTier3Amount,
            }
          : {}),
      });
    }
  } catch (error: any) {
    console.error('计算调整结果失败:', error);
    message.error(`计算调整结果失败: ${error.message || '请稍后重试'}`);

    // 恢复原值
    adjustmentForm.adjustedSurchargeAmount = surchargeAmount;
  }
}

// 注意：不再需要处理调整类型变更，因为所有调整项都在同一个页面

// 注意：前端不再计算总金额，所有计算都由后端完成

// 处理账单调整提交
async function handleAdjustConsumption() {
  if (!isAdjustmentFormValid.value) {
    message.warning('请填写完整的调整信息');
    return;
  }

  try {
    // 构建请求参数，包含所有可能的调整项
    const requestParams: any = {
      billId: Number(adjustmentForm.billId), // 确保 billId 是数字
      adjustmentReason: adjustmentForm.reason,
      // 读数调整优先于用量调整
      adjustmentReading: adjustmentForm.adjustedCurrentReadingValue,
      adjustmentAdditionalCharge: adjustmentForm.adjustedAdditionalChargeAmount,
      adjustmentSurcharge: adjustmentForm.adjustedSurchargeAmount,
    };

    // 调用后端接口
    await adjustConsumption(requestParams);

    message.success('账单调整成功');

    // 重置表单
    resetAdjustmentForm();

    // 重新加载相关数据
    if (userInfo.value.userId) {
      // 重新加载账单数据（用量调整会影响账单）
      loadBillInfoData();

      // 重新加载窗口收费数据（调整后的账单可能影响待缴费列表）
      loadPaymentData();

      // 重新加载缴费明细数据（如果在缴费明细标签页）
      if (activeTabKey.value === 'paymentDetail') {
        loadPaymentDetailData();
      }
    }
  } catch (error: any) {
    console.error('账单调整失败:', error);
    message.error(`账单调整失败: ${error.message || '请稍后重试'}`);
  }
}

// 从日期字符串中提取年月
function extractMonthFromDate(dateString) {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  } catch (error) {
    console.error('日期解析失败:', error);
    return '';
  }
}

// 处理冲正时间范围变化
function handleReversalTimeRangeChange(dates, dateStrings) {
  console.log('时间范围变化:', dates, dateStrings); // 调试日志
  if (dates && dates.length === 2 && dateStrings && dateStrings.length === 2) {
    reversalStartDate.value = dateStrings[0];
    reversalEndDate.value = dateStrings[1];
  } else {
    reversalStartDate.value = '';
    reversalEndDate.value = '';
  }
  // 重置已选择的缴费记录
  reversalForm.paymentDetailId = '';
  reversalForm.paymentNo = '';
  reversalForm.userNo = '';
  reversalForm.userName = '';
  reversalForm.billMonth = '';
  reversalForm.originalAmount = 0;
  reversalForm.reversalReason = '';
  reversalForm.reversalType = 'MANUAL';
  reversalForm.forceReversal = false;
}

// 清除冲正时间范围
function clearReversalTimeRange() {
  reversalTimeRange.value = [];
  reversalStartDate.value = '';
  reversalEndDate.value = '';
  // 重置已选择的缴费记录
  reversalForm.paymentDetailId = '';
  reversalForm.paymentNo = '';
  reversalForm.userNo = '';
  reversalForm.userName = '';
  reversalForm.billMonth = '';
  reversalForm.originalAmount = 0;
  reversalForm.reversalReason = '';
  reversalForm.reversalType = 'MANUAL';
  reversalForm.forceReversal = false;
}

// 处理冲正缴费记录选择变化
async function handleReversalPaymentChange(paymentDetailId) {
  console.log('选择的缴费记录ID:', paymentDetailId); // 调试日志
  console.log('当前缴费记录列表:', paymentDetailList.value); // 调试日志

  if (!paymentDetailList.value) {
    message.warning('缴费记录数据未加载，请稍后再试');
    return;
  }

  const selectedPayment = paymentDetailList.value.find(
    (payment) => payment.paymentDetailId === paymentDetailId
  );

  if (selectedPayment) {
    console.log('选中的缴费记录:', selectedPayment); // 调试日志
    reversalForm.paymentDetailId = paymentDetailId;
    // 使用 paymentDetailId 作为缴费编号，或者使用 transactionId 作为交易流水号
    reversalForm.paymentNo = selectedPayment.transactionId || selectedPayment.paymentDetailId || '';
    reversalForm.userNo = selectedPayment.userNo || '';
    reversalForm.userName = selectedPayment.userName || '';
    // 从缴费时间中提取月份，或者使用其他方式获取账单月份
    reversalForm.billMonth = selectedPayment.billMonth || extractMonthFromDate(selectedPayment.paymentTime) || '';
    reversalForm.originalAmount = selectedPayment.paymentAmount || 0;
    reversalForm.reversalReason = '';
    reversalForm.reversalType = 'MANUAL';
    reversalForm.forceReversal = false;
  } else {
    console.warn('未找到对应的缴费记录');
    // 重置冲正表单
    reversalForm.paymentDetailId = '';
    reversalForm.paymentNo = '';
    reversalForm.userNo = '';
    reversalForm.userName = '';
    reversalForm.billMonth = '';
    reversalForm.originalAmount = 0;
    reversalForm.reversalReason = '';
    reversalForm.reversalType = 'MANUAL';
    reversalForm.forceReversal = false;
  }
}

// 处理冲正
async function handleReversal() {
  if (!isReversalFormValid.value) {
    message.warning('请填写完整的冲正信息');
    return;
  }

  try {
    await reversalPayment({
      paymentDetailId: reversalForm.paymentDetailId,
      reversalReason: reversalForm.reversalReason,
      reversalType: reversalForm.reversalType,
      forceReversal: reversalForm.forceReversal,
    });

    message.success('冲正成功');

    // 重置表单
    reversalForm.paymentDetailId = '';
    reversalForm.paymentNo = '';
    reversalForm.userNo = '';
    reversalForm.userName = '';
    reversalForm.billMonth = '';
    reversalForm.originalAmount = 0;
    reversalForm.reversalReason = '';
    reversalForm.reversalType = 'MANUAL';
    reversalForm.forceReversal = false;

    // 重新加载数据
    if (userInfo.value.userId) {
      loadUserData(userInfo.value.userId);
    }
  } catch (error) {
    console.error('冲正失败:', error);
    message.error(`冲正失败: ${error.message || '请稍后重试'}`);
  }
}

// 用户性质字典值
const customerNatureOptions = ref<
  Array<{
    label: string;
    value: string;
  }>
>([]);
// 用水性质字典值
const useWaterNatureOptions = ref<
  Array<{
    label: string;
    value: string;
  }>
>([]);

// 获取字典值
async function fetchDictionaryData() {
  try {
    // 获取用户性质字典值
    const customerNatureRes = await dictDataInfo(
      'waterfee_user_customer_nature',
    );
    customerNatureOptions.value = customerNatureRes.map((item) => ({
      label: item.dictLabel,
      value: item.dictValue,
    }));

    // 获取用水性质字典值
    const useWaterNatureRes = await dictDataInfo(
      'waterfee_user_use_water_nature',
    );
    useWaterNatureOptions.value = useWaterNatureRes.map((item) => ({
      label: item.dictLabel,
      value: item.dictValue,
    }));
  } catch (error) {
    console.error('获取字典值失败:', error);
    message.error('获取字典值失败，请稍后重试');
  }
}

// 在组件挂载时获取字典值
onMounted(() => {
  fetchDictionaryData();
});
</script>

<template>
  <div>
    <div class="counter-payment-container">
      <!-- 搜索区域 -->
      <Card title="用户查询" :bordered="false" class="mb-4">
        <div class="search-container">
          <div class="search-input-wrapper">
            <AutoComplete
              v-model:value="searchForm.keyword"
              :options="searchOptions"
              :filter-option="false"
              placeholder="请输入用户编号或姓名"
              style="width: 100%"
              @search="handleSearchInput"
              @select="handleSelectUser"
            >
              <template #option="{ value, label, data }">
                <div class="search-option">
                  <div class="search-option-title">{{ label }}</div>
                  <div class="search-option-desc">{{ data.desc }}</div>
                </div>
              </template>
            </AutoComplete>
          </div>
          <div class="search-buttons">
            <Button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              查询
            </Button>
            <Button class="ml-2" @click="resetSearch">
              <template #icon><ReloadOutlined /></template>
              重置
            </Button>
          </div>
        </div>
      </Card>

      <!-- 用户信息展示区域 -->
      <Card
        v-if="userInfo.userNo"
        title="用户信息"
        :bordered="false"
        class="mb-4"
      >
        <Descriptions :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }">
          <DescriptionsItem label="用户编号">
            {{ userInfo.userNo }}
          </DescriptionsItem>
          <DescriptionsItem label="用户姓名">
            {{ userInfo.userName }}
          </DescriptionsItem>
          <DescriptionsItem label="联系电话">
            {{ userInfo.phoneNumber }}
          </DescriptionsItem>
          <DescriptionsItem label="用水地址">
            {{ userInfo.address }}
          </DescriptionsItem>
          <DescriptionsItem label="用户性质">
            {{
              customerNatureOptions.find(
                (option) => option.value === userInfo.customerNature,
              )?.label || userInfo.customerNature
            }}
          </DescriptionsItem>
          <DescriptionsItem label="用水性质">
            {{
              useWaterNatureOptions.find(
                (option) => option.value === userInfo.useWaterNature,
              )?.label || userInfo.useWaterNature
            }}
          </DescriptionsItem>
          <DescriptionsItem label="用水人数">
            {{ userInfo.useWaterNumber }}
          </DescriptionsItem>
          <DescriptionsItem label="供水日期">
            {{ userInfo.supplyDate }}
          </DescriptionsItem>
        </Descriptions>
      </Card>

      <!-- 标签页区域 -->
      <Card :bordered="false">
        <Tabs v-model:active-key="activeTabKey" @change="handleTabChange">
          <!-- 窗口收费 -->
          <TabPane key="payment" tab="窗口收费">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="bill-table-container">
                <div class="table-header">
                  <div class="table-title">待缴费账单</div>
                  <div class="table-actions">
                    <Button
                      type="primary"
                      :disabled="selectedBills.length === 0"
                      @click="handlePayBills"
                    >
                      <template #icon><DollarOutlined /></template>
                      缴费
                    </Button>
                  </div>
                </div>
                <Table
                  ref="billTableRef"
                  :data-source="billList"
                  :columns="billColumnsWithActions"
                  :row-key="(record) => record.billId"
                  :row-selection="{ onChange: handleBillSelectionChange }"
                  :scroll="{ y: 300 }"
                  :pagination="false"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                      <Button
                        type="link"
                        size="small"
                        @click="openBillAdjustModal(record)"
                      >
                        金额调整
                      </Button>
                    </template>
                  </template>
                  <template #emptyText>
                    <div class="empty-data">暂无待缴费账单</div>
                  </template>
                </Table>
              </div>

              <div class="payment-summary" v-if="selectedBills.length > 0">
                <div class="summary-item">
                  <span class="label">已选账单数:</span>
                  <span class="value">{{ selectedBills.length }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">应缴总金额:</span>
                  <span class="value amount">{{ totalPaymentAmount }} 元</span>
                </div>
              </div>
            </template>
          </TabPane>

          <!-- 缴费明细 -->
          <TabPane key="paymentDetail" tab="缴费明细">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <Table
                ref="paymentDetailTableRef"
                :data-source="paymentDetailList"
                :columns="paymentDetailColumns"
                :row-key="(record) => record.paymentDetailId || record.id"
                :scroll="{ y: 300 }"
                :pagination="false"
              >
                <template #emptyText>
                  <div class="empty-data">暂无缴费记录</div>
                </template>
              </Table>
            </template>
          </TabPane>

          <!-- 账单信息 -->
          <TabPane key="billInfo" tab="账单信息">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="bill-filter-container">
                <div class="filter-item">
                  <span class="filter-label">账单月份：</span>
                  <DatePicker
                    v-model:value="billMonthFilter"
                    picker="month"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    placeholder="选择月份"
                    style="width: 200px"
                    @change="handleBillMonthChange"
                    allow-clear
                  />
                </div>
              </div>
              <Table
                ref="allBillTableRef"
                :data-source="allBillList"
                :columns="billColumns"
                :row-key="(record) => record.billId"
                :scroll="{ y: 300 }"
                :pagination="false"
              >
                <template #emptyText>
                  <div class="empty-data">暂无账单信息</div>
                </template>
              </Table>
            </template>
          </TabPane>

          <!-- 抄表计费 -->
          <!--<TabPane key="meterReading" tab="抄表计费">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <Table
                ref="meterReadingTableRef"
                :dataSource="meterReadingList"
                :columns="meterReadingColumns"
                :scroll="{ y: 300 }"
                :pagination="false"
              >
                <template #emptyText>
                  <div class="empty-data">暂无抄表记录</div>
                </template>
              </Table>
            </template>
          </TabPane> -->

          <!-- 预存充值 -->
          <TabPane key="deposit" tab="预存充值">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="deposit-container">
                <div class="deposit-info">
                  <div class="info-item">
                    <span class="label">当前预存余额:</span>
                    <span class="value amount"
                      >{{ depositBalance || 0 }} 元</span
                    >
                  </div>
                </div>
                <div class="deposit-form">
                  <Form layout="vertical" :model="depositForm">
                    <FormItem label="充值金额" required>
                      <InputNumber
                        v-model:value="depositForm.amount"
                        :min="0"
                        :precision="2"
                        style="width: 200px"
                        addon-after="元"
                      />
                    </FormItem>
                    <FormItem label="支付方式" required>
                      <Select
                        v-model:value="depositForm.paymentMethod"
                        style="width: 200px"
                        :options="
                          paymentMethodOptions.filter(
                            (item) => item.value !== 'DEPOSIT',
                          )
                        "
                      />
                    </FormItem>
                    <FormItem label="收费员" required>
                      <Input
                        v-model:value="depositForm.tollCollector"
                        placeholder="请输入收费员姓名"
                        allow-clear
                      />
                    </FormItem>
                    <FormItem label="备注">
                      <Input
                        v-model:value="depositForm.remark"
                        placeholder="请输入备注信息"
                        allow-clear
                      />
                    </FormItem>
                    <FormItem>
                      <Button
                        type="primary"
                        :disabled="!depositForm.amount || !depositForm.paymentMethod || !depositForm.tollCollector || depositForm.tollCollector.trim() === ''"
                        @click="handleDeposit"
                      >
                        <template #icon><PlusOutlined /></template>
                        充值
                      </Button>
                    </FormItem>
                  </Form>
                </div>
              </div>
            </template>
          </TabPane>

          <!-- 余额退款 -->
          <TabPane key="refund" tab="余额退款">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="refund-container">
                <div class="refund-info">
                  <div class="info-item">
                    <span class="label">当前预存余额:</span>
                    <span class="value amount"
                      >{{ depositBalance || 0 }} 元</span
                    >
                  </div>
                </div>
                <div class="refund-form">
                  <Form layout="vertical" :model="refundForm">
                    <FormItem label="退款金额" required>
                      <InputNumber
                        v-model:value="refundForm.refundAmount"
                        :min="0"
                        :max="depositBalance"
                        :precision="2"
                        style="width: 200px"
                        addon-after="元"
                        placeholder="请输入退款金额"
                      />
                      <div style="margin-top: 4px; color: #666; font-size: 12px;">
                        最大可退款金额: {{ depositBalance || 0 }} 元
                      </div>
                    </FormItem>
                    <FormItem label="退款原因" required>
                      <Input
                        v-model:value="refundForm.refundReason"
                        placeholder="请输入退款原因"
                        style="width: 300px"
                        allow-clear
                      />
                    </FormItem>
                    <FormItem label="退款方式" required>
                      <Select
                        v-model:value="refundForm.refundMethod"
                        style="width: 200px"
                        :options="refundMethodOptions"
                        placeholder="请选择退款方式"
                      />
                    </FormItem>
                    <!-- 银行卡退款时的额外字段 -->
                    <template v-if="refundForm.refundMethod === 'BANK_CARD'">
                      <FormItem label="银行卡号" required>
                        <Input
                          v-model:value="refundForm.bankCardNo"
                          placeholder="请输入银行卡号"
                          style="width: 250px"
                          allow-clear
                        />
                      </FormItem>
                      <FormItem label="开户行" required>
                        <Input
                          v-model:value="refundForm.bankName"
                          placeholder="请输入开户行"
                          style="width: 250px"
                          allow-clear
                        />
                      </FormItem>
                      <FormItem label="账户名" required>
                        <Input
                          v-model:value="refundForm.accountName"
                          placeholder="请输入账户名"
                          style="width: 200px"
                          allow-clear
                        />
                      </FormItem>
                    </template>
                    <FormItem label="备注">
                      <Input
                        v-model:value="refundForm.remark"
                        placeholder="请输入备注信息"
                        style="width: 300px"
                        allow-clear
                      />
                    </FormItem>
                    <FormItem label="强制退款">
                      <Switch
                        v-model:checked="refundForm.forceRefund"
                        checked-children="是"
                        un-checked-children="否"
                      />
                      <div style="margin-top: 4px; color: #666; font-size: 12px;">
                        开启后将跳过部分校验
                      </div>
                    </FormItem>
                    <FormItem>
                      <Button
                        type="primary"
                        danger
                        :disabled="!refundForm.refundAmount || !refundForm.refundReason || !refundForm.refundMethod || refundForm.refundAmount > depositBalance"
                        @click="handleRefund"
                      >
                        <template #icon><RollbackOutlined /></template>
                        退款
                      </Button>
                    </FormItem>
                  </Form>
                </div>
              </div>
            </template>
          </TabPane>

          <!-- 缴费冲正 -->
          <TabPane key="reversal" tab="缴费冲正">
            <div v-if="!userInfo.userId" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="reversal-container">
                <div class="reversal-form">
                  <Form layout="vertical" :model="reversalForm">
                    <FormItem label="缴费时间范围">
                      <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                        <RangePicker
                          v-model:value="reversalTimeRange"
                          style="flex: 1"
                          :placeholder="['开始日期', '结束日期']"
                          format="YYYY-MM-DD"
                          @change="handleReversalTimeRangeChange"
                        />
                        <Button
                          v-if="reversalStartDate || reversalEndDate"
                          type="link"
                          size="small"
                          @click="clearReversalTimeRange"
                        >
                          清除
                        </Button>
                      </div>
                      <div style="display: flex; gap: 4px; flex-wrap: wrap; margin-bottom: 8px;">
                        <Button size="small" @click="setTimeRange('today')">今天</Button>
                        <Button size="small" @click="setTimeRange('yesterday')">昨天</Button>
                        <Button size="small" @click="setTimeRange('week')">最近7天</Button>
                        <Button size="small" @click="setTimeRange('month')">本月</Button>
                        <Button size="small" @click="setTimeRange('lastMonth')">上月</Button>
                      </div>
                      <div style="color: #666; font-size: 12px;">
                        可选择时间范围来过滤缴费记录
                      </div>
                    </FormItem>
                    <FormItem label="选择缴费记录" required>
                      <Select
                        v-model:value="reversalForm.paymentDetailId"
                        style="width: 100%"
                        placeholder="请选择需要冲正的缴费记录"
                        :options="reversalPaymentOptions"
                        @change="handleReversalPaymentChange"
                      />
                      <div v-if="reversalStartDate && reversalEndDate" style="margin-top: 4px; color: #666; font-size: 12px;">
                        显示 {{ reversalStartDate }} 至 {{ reversalEndDate }} 的缴费记录
                      </div>
                      <div v-if="reversalPaymentOptions.length === 0 && paymentDetailList.length > 0" style="margin-top: 4px; color: #ff4d4f; font-size: 12px;">
                        所选时间范围内无可冲正的缴费记录
                      </div>
                    </FormItem>
                    <FormItem v-if="reversalForm.paymentDetailId" label="缴费编号">
                      <Input
                        :value="reversalForm.paymentNo"
                        disabled
                        style="width: 300px"
                      />
                    </FormItem> 
                    <FormItem v-if="reversalForm.paymentDetailId" label="账单月份">
                      <Input
                        :value="reversalForm.billMonth"
                        disabled
                        style="width: 200px"
                      />
                    </FormItem>
                    <FormItem v-if="reversalForm.paymentDetailId" label="原缴费金额">
                      <InputNumber
                        :value="reversalForm.originalAmount"
                        disabled
                        style="width: 200px"
                        addon-after="元"
                      />
                    </FormItem>
                    <FormItem v-if="reversalForm.paymentDetailId" label="冲正原因" required>
                      <Input
                        v-model:value="reversalForm.reversalReason"
                        placeholder="请输入冲正原因"
                        allow-clear
                      />
                    </FormItem>
                    <FormItem v-if="reversalForm.paymentDetailId" label="冲正类型">
                      <Select
                        v-model:value="reversalForm.reversalType"
                        style="width: 200px"
                        placeholder="请选择冲正类型"
                        :options="[
                          { label: '手动冲正', value: 'MANUAL' },
                          { label: '系统冲正', value: 'SYSTEM' },
                        ]"
                      />
                    </FormItem>
                    <FormItem v-if="reversalForm.paymentDetailId" label="强制冲正">
                      <Switch
                        v-model:checked="reversalForm.forceReversal"
                        checked-children="是"
                        un-checked-children="否"
                      />
                      <span style="margin-left: 8px; color: #666;">
                        强制冲正将跳过部分校验
                      </span>
                    </FormItem>
                    <FormItem v-if="reversalForm.paymentDetailId">
                      <Button
                        type="primary"
                        :disabled="!isReversalFormValid"
                        @click="handleReversal"
                      >
                        <template #icon><UndoOutlined /></template>
                        提交冲正
                      </Button>
                    </FormItem>
                  </Form>
                </div>
              </div>
            </template>
          </TabPane>
        </Tabs>
      </Card>
    </div>

    <!-- 缴费弹窗 -->
    <Modal
      v-model:visible="paymentModalVisible"
      title="账单缴费"
      :width="600"
      :mask-closable="false"
      @cancel="cancelPayment"
    >
      <div class="payment-modal-content">
        <div class="payment-bills-summary">
          <div class="summary-item">
            <span class="label">账单数量:</span>
            <span class="value">{{ selectedBills.length }}</span>
          </div>
          <div class="summary-item">
            <span class="label">应缴总金额:</span>
            <span class="value amount">{{ totalPaymentAmount }} 元</span>
          </div>
        </div>

        <Form layout="vertical" :model="paymentForm">
          <FormItem label="实缴金额" required>
            <InputNumber
              v-model:value="paymentForm.amount"
              :min="totalAmountShouldPay"
              :precision="2"
              style="width: 100%"
              addon-after="元"
              placeholder="请输入实缴金额"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              最低缴费金额: {{ totalAmountShouldPay }} 元
            </div>
          </FormItem>
          <FormItem label="支付方式" required>
            <Select
              v-model:value="paymentForm.paymentMethod"
              style="width: 100%"
              :options="paymentMethodOptions"
            />
          </FormItem>
          <FormItem label="收费员" required>
            <Input
              v-model:value="paymentForm.tollCollector"
              placeholder="请输入收费员姓名"
              allow-clear
            />
          </FormItem>
          <FormItem label="备注">
            <Input
              v-model:value="paymentForm.remark"
              placeholder="请输入备注信息"
              allow-clear
            />
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <Button @click="cancelPayment">取消</Button>
        <Button
          type="primary"
          :disabled="!isPaymentFormValid"
          @click="confirmPayment"
        >
          确认缴费
        </Button>
      </template>
    </Modal>

    <!-- 金额调整弹窗 -->
    <Modal
      v-model:open="billAdjustModalVisible"
      title="账单金额调整"
      width="500px"
      @ok="confirmBillAdjust"
      @cancel="cancelBillAdjust"
    >
      <Form layout="vertical" style="margin-top: 20px">
        <FormItem label="账单信息" v-if="currentAdjustBill">
          <div style="background: #f5f5f5; padding: 12px; border-radius: 4px">
            <div>账单编号: {{ currentAdjustBill.billNumber }}</div>
            <div>用户姓名: {{ currentAdjustBill.userName }}</div>
            <div>当前总金额: {{ currentAdjustBill.totalAmount }} 元</div>
            <div>
              当前调整金额: {{ currentAdjustBill.adjustmentsAmount || 0 }} 元
            </div>
          </div>
        </FormItem>
        <FormItem label="调整金额" required>
          <InputNumber
            v-model:value="billAdjustForm.adjustmentsAmount"
            :precision="2"
            style="width: 100%"
            addon-after="元"
            placeholder="请输入调整金额（正数为增加，负数为减少）"
          />
          <div style="margin-top: 4px; color: #666; font-size: 12px;">
            正数表示增加金额，负数表示减少金额
          </div>
        </FormItem>
        <FormItem label="调整原因" required>
          <Input.TextArea
            v-model:value="billAdjustForm.adjustmentReason"
            placeholder="请输入调整原因"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.counter-payment-container {
  .search-container {
    display: flex;
    align-items: center;
    gap: 16px;

    .search-input-wrapper {
      flex: 1;
      max-width: 500px;
    }

    .search-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .search-option {
    padding: 4px 0;

    .search-option-title {
      font-weight: 500;
    }

    .search-option-desc {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .empty-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #999;
    font-size: 16px;
  }

  .bill-table-container {
    margin-bottom: 16px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .table-title {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .bill-filter-container {
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    .filter-item {
      display: flex;
      align-items: center;
      margin-right: 16px;

      .filter-label {
        margin-right: 8px;
        white-space: nowrap;
      }
    }
  }

  .payment-summary {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 16px;
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-radius: 4px;

    .summary-item {
      margin-left: 24px;

      .label {
        margin-right: 8px;
        color: #666;
      }

      .value {
        font-weight: 500;

        &.amount {
          color: #f5222d;
          font-size: 16px;
        }
      }
    }
  }

  .deposit-container,
  .adjustment-container,
  .refund-container {
    padding: 16px;

    .deposit-info {
      margin-bottom: 24px;
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 4px;

      .info-item {
        .label {
          margin-right: 8px;
          color: #666;
        }

        .value {
          font-weight: 500;

          &.amount {
            color: #52c41a;
            font-size: 16px;
          }
        }
      }
    }

    .deposit-form,
    .adjustment-form,
    .refund-form {
      max-width: 800px;
    }

    .bill-info-card {
      margin-bottom: 20px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      padding: 16px;
      background-color: #fafafa;
    }

    .adjustment-section {
      margin-bottom: 24px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      padding: 16px;
      background-color: #fafafa;

      h3 {
        margin-top: 0;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e8e8e8;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
}

.payment-modal-content {
  .payment-bills-summary {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 4px;

    .summary-item {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        margin-right: 8px;
        color: #666;
      }

      .value {
        font-weight: 500;

        &.amount {
          color: #f5222d;
          font-size: 16px;
        }
      }
    }
  }
}

.empty-data {
  padding: 32px 0;
  text-align: center;
  color: #999;
}

.ladder-price-detail {
  margin-top: 16px;

  .ladder-tier {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }

    .ladder-tier-header {
      padding: 8px 12px;
      background-color: #f5f5f5;
      font-weight: 500;
      border-bottom: 1px solid #f0f0f0;
    }

    .ladder-tier-content {
      padding: 12px;
      display: flex;
      flex-wrap: wrap;

      .ladder-tier-item {
        width: 50%;
        padding: 4px 0;

        .label {
          margin-right: 8px;
          color: #666;
        }

        .value {
          font-weight: 500;
        }
      }
    }
  }
}
</style>
