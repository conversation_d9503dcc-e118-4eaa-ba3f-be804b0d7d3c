import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Select',
    fieldName: 'channelCode',
    label: '渠道编码',
    componentProps: {
      placeholder: '请选择渠道编码',
      options: [
        { label: '银联', value: 'UNIONPAY' },
        { label: '微信', value: 'WECHAT' },
        { label: '支付宝', value: 'ALIPAY' },
        { label: '银行', value: 'BANK' },
      ],
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'localOrderNo',
    label: '本地订单号',
    componentProps: {
      placeholder: '请输入本地订单号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'thirdOrderNo',
    label: '第三方订单号',
    componentProps: {
      placeholder: '请输入第三方订单号',
      allowClear: true,
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'amount',
    label: '交易金额',
    componentProps: {
      placeholder: '请输入交易金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '对账状态',
    componentProps: {
      placeholder: '请选择对账状态',
      options: [
        { label: '待对账', value: 'PENDING' },
        { label: '对账成功', value: 'MATCHED' },
        { label: '对账失败', value: 'MISMATCHED' },
        { label: '异常', value: 'EXCEPTION' },
      ],
      allowClear: true,
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'reconciliationDate',
    label: '对账日期',
    componentProps: {
      placeholder: '请选择对账日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键',
    field: 'id',
  },
  {
    title: '渠道编码',
    field: 'channelCode',
  },
  {
    title: '本地订单号',
    field: 'localOrderNo',
  },
  {
    title: '第三方订单号',
    field: 'thirdOrderNo',
  },
  {
    title: '金额',
    field: 'amount',
  },
  {
    title: '状态',
    field: 'status',
  },
  {
    title: '对账日期',
    field: 'reconciliationDate',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Select',
    fieldName: 'channelCode',
    label: '渠道编码',
    rules: 'required',
    componentProps: {
      placeholder: '请选择渠道编码',
      options: [
        { label: '银联', value: 'UNIONPAY' },
        { label: '微信', value: 'WECHAT' },
        { label: '支付宝', value: 'ALIPAY' },
        { label: '银行', value: 'BANK' },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'channelName',
    label: '渠道名称',
    componentProps: {
      placeholder: '请输入渠道名称',
    },
  },
  {
    component: 'Input',
    fieldName: 'localOrderNo',
    label: '本地订单号',
    rules: 'required',
    componentProps: {
      placeholder: '请输入本地订单号',
    },
  },
  {
    component: 'Input',
    fieldName: 'thirdOrderNo',
    label: '第三方订单号',
    rules: 'required',
    componentProps: {
      placeholder: '请输入第三方订单号',
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'amount',
    label: '交易金额',
    rules: 'required',
    componentProps: {
      placeholder: '请输入交易金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
      addonAfter: '元',
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'localAmount',
    label: '本地金额',
    componentProps: {
      placeholder: '请输入本地金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
      addonAfter: '元',
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'thirdAmount',
    label: '第三方金额',
    componentProps: {
      placeholder: '请输入第三方金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
      addonAfter: '元',
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '对账状态',
    rules: 'required',
    componentProps: {
      placeholder: '请选择对账状态',
      options: [
        { label: '待对账', value: 'PENDING' },
        { label: '对账成功', value: 'MATCHED' },
        { label: '对账失败', value: 'MISMATCHED' },
        { label: '异常', value: 'EXCEPTION' },
      ],
    },
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择对账日期',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'reconciliationDate',
    label: '对账日期',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择交易时间',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'transactionTime',
    label: '交易时间',
  },
  {
    component: 'Input',
    fieldName: 'operator',
    label: '操作员',
    componentProps: {
      placeholder: '请输入操作员',
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
];
