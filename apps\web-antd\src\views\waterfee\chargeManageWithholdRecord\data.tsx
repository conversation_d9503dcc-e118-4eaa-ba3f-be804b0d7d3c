import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    componentProps: {
      placeholder: '请输入用户ID',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '用户编号',
    componentProps: {
      placeholder: '请输入用户编号',
      allowClear: true,
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'amount',
    label: '扣款金额',
    componentProps: {
      placeholder: '请输入扣款金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '扣款状态',
    componentProps: {
      placeholder: '请选择扣款状态',
      options: [
        { label: '待扣款', value: 'PENDING' },
        { label: '扣款成功', value: 'SUCCESS' },
        { label: '扣款失败', value: 'FAILED' },
        { label: '已取消', value: 'CANCELLED' },
      ],
      allowClear: true,
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'withholdTime',
    label: '扣款时间',
    componentProps: {
      placeholder: '请选择扣款时间',
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键',
    field: 'id',
  },
  {
    title: '用户ID',
    field: 'userId',
  },
  {
    title: '扣款金额',
    field: 'amount',
  },
  {
    title: '扣款时间',
    field: 'withholdTime',
  },
  {
    title: '状态',
    field: 'status',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    rules: 'required',
    componentProps: {
      placeholder: '请输入用户ID',
    },
  },
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '用户编号',
    componentProps: {
      placeholder: '请输入用户编号',
    },
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用户姓名',
    componentProps: {
      placeholder: '请输入用户姓名',
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'amount',
    label: '扣款金额',
    rules: 'required',
    componentProps: {
      placeholder: '请输入扣款金额',
      min: 0,
      precision: 2,
      style: { width: '100%' },
      addonAfter: '元',
    },
  },
  {
    component: 'Input',
    fieldName: 'bankAccount',
    label: '银行账号',
    componentProps: {
      placeholder: '请输入银行账号',
    },
  },
  {
    component: 'Input',
    fieldName: 'bankName',
    label: '开户行',
    componentProps: {
      placeholder: '请输入开户行',
    },
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择扣款时间',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'withholdTime',
    label: '扣款时间',
    rules: 'required',
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '扣款状态',
    rules: 'required',
    componentProps: {
      placeholder: '请选择扣款状态',
      options: [
        { label: '待扣款', value: 'PENDING' },
        { label: '扣款成功', value: 'SUCCESS' },
        { label: '扣款失败', value: 'FAILED' },
        { label: '已取消', value: 'CANCELLED' },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'transactionId',
    label: '交易流水号',
    componentProps: {
      placeholder: '请输入交易流水号',
    },
  },
  {
    component: 'Input',
    fieldName: 'operator',
    label: '操作员',
    componentProps: {
      placeholder: '请输入操作员',
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
];
