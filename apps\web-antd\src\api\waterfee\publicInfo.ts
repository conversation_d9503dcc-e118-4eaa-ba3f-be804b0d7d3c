import type {
  PublicInfoModel,
  PublicInfoParams,
} from './model/publicInfoModel';

import type { ID, PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/publicInfo/list',
  publish = '/waterfee/publicInfo/publish',
  root = '/waterfee/publicInfo',
}

/**
 * 获取公共信息列表
 */
export function publicInfoList(params?: PublicInfoParams) {
  return requestClient.get<PageResult<PublicInfoModel>>(Api.list, { params });
}

/**
 * 获取公共信息详情
 */
export function publicInfoDetail(id: ID) {
  return requestClient.get<PublicInfoModel>(`${Api.root}/${id}`);
}

/**
 * 新增公共信息
 */
export function publicInfoAdd(data: Partial<PublicInfoModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改公共信息
 */
export function publicInfoUpdate(data: Partial<PublicInfoModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除公共信息
 */
export function publicInfoRemove(ids: Array<ID> | ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${ids}`);
}

/**
 * 发布公共信息
 */
export function publicInfoPublish(id: ID) {
  return requestClient.putWithMsg<void>(`${Api.publish}/${id}`);
}

/**
 * 批量发布公共信息
 */
export function publicInfoPublishBatch(ids: Array<ID>) {
  return requestClient.putWithMsg<void>(`${Api.publish}/batch`, { ids });
}
