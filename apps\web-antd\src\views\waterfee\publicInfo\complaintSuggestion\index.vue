<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ComplaintSuggestionVO } from '#/api/waterfee/complaintSuggestion/model.d';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  ComplaintSuggestionExport,
  delComplaintSuggestion,
  listComplaintSuggestion,
} from '#/api/waterfee/complaintSuggestion';
import { commonDownloadExcel } from '#/utils/file/download';

import complaintSuggestionDrawer from './complaintSuggestion-drawer.vue';
import { columns, querySchema } from './data';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await listComplaintSuggestion({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'complaintSuggestionId',
  },
  id: 'waterfee-complaintSuggestion-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [ComplaintSuggestionDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: complaintSuggestionDrawer,
});

function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

async function handleEdit(record: ComplaintSuggestionVO) {
  drawerApi.setData({ id: record.complaintSuggestionId });
  drawerApi.open();
}

async function handleDelete(row: ComplaintSuggestionVO) {
  await delComplaintSuggestion([row.complaintSuggestionId]);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map(
    (row: ComplaintSuggestionVO) => row.complaintSuggestionId,
  );
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await delComplaintSuggestion(ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(
    ComplaintSuggestionExport,
    '投诉建议数据',
    tableApi.formApi.form.values,
  );
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="投诉建议列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['waterfee:complaintSuggestion:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['waterfee:complaintSuggestion:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['waterfee:complaintSuggestion:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['waterfee:complaintSuggestion:edit']"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['waterfee:complaintSuggestion:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <ComplaintSuggestionDrawer @reload="tableApi.query()" />
  </Page>
</template>
