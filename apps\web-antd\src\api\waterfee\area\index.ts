import type { Area } from './model';

import type { ID } from '#/api/common';

import { requestClient } from '#/api/request';
import { ensureMeterIdString } from '#/api/waterfee/meter/index';

enum Api {
  areaList = '/waterfee/area/list',
  areaNodeInfo = '/waterfee/area/list/exclude',
  areaOptions = '/waterfee/area/getSelectList',
  root = '/waterfee/area',
}

/**
 * 营业区域列表
 * @returns list
 */
export function areaList(params?: { areaName?: string; status?: string }) {
  return requestClient.get<Area[]>(Api.areaList, { params });
}

export function areaOptions(params?: { areaName?: string; status?: string }) {
  return requestClient.get<Area[]>(Api.areaOptions, { params });
}

/**
 * 查询营业区域列表（排除节点）
 * @param areaId 营业区域ID
 * @returns void
 */
export function areaNodeList(areaId: ID) {
  return requestClient.get<Area[]>(`${Api.areaNodeInfo}/${areaId}`);
}

/**
 * 营业区域详情
 * @param areaId 营业区域id
 * @returns 营业区域信息
 */
export function areaInfo(areaId: ID) {
  return requestClient.get<Area>(`${Api.root}/${areaId}`);
}

/**
 * 营业区域新增
 * @param data 参数
 */
export function areaAdd(data: Partial<Area>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 营业区域更新
 * @param data 参数
 */
export function areaUpdate(data: Partial<Area>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 注意这里只允许单删除
 * @param areaId ID
 * @returns void
 */
export function areaRemove(areaId: ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${areaId}`);
}

/**
 * 批量获取区域信息
 * @param areaIds 区域ID数组
 * @returns 区域信息列表
 */
export function areaInfoBatch(areaIds: ID[]) {
  // 转为字符串数组，确保 ID 精度（如果是大数）
  const safeIds = areaIds.map((id) => ensureMeterIdString(id));

  // 使用 POST 请求传数组，避免 URL 太长
  return requestClient.post<Area[]>('/waterfee/meter/areas', safeIds);
}
