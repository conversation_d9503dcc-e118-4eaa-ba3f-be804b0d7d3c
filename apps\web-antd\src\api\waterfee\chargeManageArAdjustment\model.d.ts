export interface ChargeManageArAdjustmentVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 水表编号
   */
  meterNo: string;

  /**
   * 账期
   */
  billPeriod: string;

  /**
   * 原应收金额
   */
  originalAmount: number;

  /**
   * 调整后金额
   */
  adjustedAmount: number;

  /**
   * 调整原因
   */
  reason: string;

  /**
   * 
   */
  remark: string;

}

export interface ChargeManageArAdjustmentForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 水表编号
   */
  meterNo?: string;

  /**
   * 账期
   */
  billPeriod?: string;

  /**
   * 原应收金额
   */
  originalAmount?: number;

  /**
   * 调整后金额
   */
  adjustedAmount?: number;

  /**
   * 调整原因
   */
  reason?: string;

  /**
   * 
   */
  remark?: string;

}

export interface ChargeManageArAdjustmentQuery extends PageQuery {

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 水表编号
   */
  meterNo?: string;

  /**
   * 账期
   */
  billPeriod?: string;

  /**
   * 原应收金额
   */
  originalAmount?: number;

  /**
   * 调整后金额
   */
  adjustedAmount?: number;

  /**
   * 调整原因
   */
  reason?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



