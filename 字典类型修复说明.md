# 字典类型修复说明

## 问题描述
在完善抽屉表单字段时，使用了一些尚未在系统中定义的字典类型，如 `waterfee_withhold_status`、`waterfee_income_type` 等，导致下拉选择器无数据显示。

## 解决方案
将不存在的字典类型替换为静态选项数组，确保表单能够正常显示和使用。

## 修复的字典类型

### 1. 营业外收入记录 (chargeManageNonOperatingIncome)

#### 收入类型 (incomeType)
**原字典**: `waterfee_income_type`
**修复为静态选项**:
```javascript
options: [
  { label: '滞纳金', value: 'LATE_FEE' },
  { label: '违约金', value: 'PENALTY' },
  { label: '手续费', value: 'SERVICE_FEE' },
  { label: '其他收入', value: 'OTHER' },
]
```

#### 收款方式 (paymentMethod)
**原字典**: `waterfee_payment_method`
**修复为静态选项**:
```javascript
options: [
  { label: '现金', value: 'CASH' },
  { label: '银行卡', value: 'BANK_CARD' },
  { label: '微信', value: 'WECHAT' },
  { label: '支付宝', value: 'ALIPAY' },
  { label: '银行转账', value: 'BANK_TRANSFER' },
]
```

### 2. 代扣记录 (chargeManageWithholdRecord)

#### 扣款状态 (status)
**原字典**: `waterfee_withhold_status`
**修复为静态选项**:
```javascript
options: [
  { label: '待扣款', value: 'PENDING' },
  { label: '扣款成功', value: 'SUCCESS' },
  { label: '扣款失败', value: 'FAILED' },
  { label: '已取消', value: 'CANCELLED' },
]
```

### 3. 代扣配置信息 (chargeManageWithholdConfig)

#### 是否签约 (signed)
**原字典**: `yes_no`
**修复为静态选项**:
```javascript
options: [
  { label: '是', value: '1' },
  { label: '否', value: '0' },
]
```

### 4. 违约金调整 (chargeManagePenaltyAdjustment)

#### 调整类型 (adjustmentType)
**原字典**: `waterfee_penalty_adjustment_type`
**修复为静态选项**:
```javascript
options: [
  { label: '减免', value: 'REDUCTION' },
  { label: '增加', value: 'INCREASE' },
  { label: '调整', value: 'ADJUSTMENT' },
  { label: '取消', value: 'CANCEL' },
]
```

### 5. 应收账分账 (chargeManageArAdjustment)

#### 调整类型 (adjustmentType)
**原字典**: `waterfee_ar_adjustment_type`
**修复为静态选项**:
```javascript
options: [
  { label: '水费调整', value: 'WATER_FEE' },
  { label: '违约金调整', value: 'PENALTY' },
  { label: '附加费调整', value: 'ADDITIONAL_FEE' },
  { label: '其他调整', value: 'OTHER' },
]
```

### 6. 第三方对账记录 (chargeManageThirdParty)

#### 渠道编码 (channelCode)
**原字典**: `waterfee_channel_code`
**修复为静态选项**:
```javascript
options: [
  { label: '银联', value: 'UNIONPAY' },
  { label: '微信', value: 'WECHAT' },
  { label: '支付宝', value: 'ALIPAY' },
  { label: '银行', value: 'BANK' },
]
```

#### 对账状态 (status)
**原字典**: `waterfee_reconciliation_status`
**修复为静态选项**:
```javascript
options: [
  { label: '待对账', value: 'PENDING' },
  { label: '对账成功', value: 'MATCHED' },
  { label: '对账失败', value: 'MISMATCHED' },
  { label: '异常', value: 'EXCEPTION' },
]
```

### 7. 用户关系维护 (chargeManageUserRelation)

#### 关系类型 (relationType)
**原字典**: `waterfee_user_relation_type`
**修复为静态选项**:
```javascript
options: [
  { label: '单位-职工', value: 'COMPANY_EMPLOYEE' },
  { label: '业主-租户', value: 'OWNER_TENANT' },
  { label: '父子关系', value: 'PARENT_CHILD' },
  { label: '其他关系', value: 'OTHER' },
]
```

#### 关系状态 (status)
**原字典**: `waterfee_relation_status`
**修复为静态选项**:
```javascript
options: [
  { label: '有效', value: 'ACTIVE' },
  { label: '无效', value: 'INACTIVE' },
  { label: '暂停', value: 'SUSPENDED' },
]
```

## 修复效果

### 1. 立即可用
- 所有下拉选择器现在都有可选项
- 表单可以正常提交和保存
- 用户体验得到改善

### 2. 数据一致性
- 使用标准的英文值作为选项值
- 中文标签便于用户理解
- 值的命名遵循常见的业务规范

### 3. 扩展性
- 静态选项易于维护和修改
- 后续可以轻松迁移到数据库字典
- 支持国际化扩展

## 后续建议

### 1. 字典管理优化
建议在系统的字典管理模块中添加这些字典类型：
- 创建对应的字典类型
- 添加字典数据项
- 将静态选项迁移到数据库

### 2. 统一字典规范
- 建立字典命名规范
- 统一字典值的格式
- 完善字典管理流程

### 3. 代码重构
后续可以将静态选项替换为 `getDictOptions()` 调用：
```javascript
// 当字典数据准备好后
options: getDictOptions('waterfee_income_type')
```

## 技术细节

### 选项值设计原则
1. **英文大写**: 使用英文大写字母和下划线
2. **语义明确**: 值的含义一目了然
3. **简洁易懂**: 避免过长的值
4. **标准化**: 遵循行业标准命名

### 标签设计原则
1. **中文显示**: 便于中文用户理解
2. **简洁明了**: 避免冗长的描述
3. **业务相关**: 符合水费管理业务场景
4. **用户友好**: 符合用户使用习惯

### 兼容性考虑
- 保持与现有代码的兼容性
- 支持后续的字典系统集成
- 便于数据迁移和升级

这些修复确保了表单的正常使用，同时为后续的字典系统完善奠定了基础。
