import type {
  IncomeModel,
  IncomeParams,
  WaterfeeIncomeSummaryVo,
} from './model/incomeModel';

import type { ID, PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  export = '/waterfee/income/record/export',
  incomeByUser = '/waterfee/income/record/user',
  incomeList = '/waterfee/income/record/list',
  root = '/waterfee/income/record',
  summary = '/waterfee/income/record/summary',
}

/**
 * 获取收入记录列表
 * @param params 查询参数
 * @returns 收入记录列表
 */
export function incomeList(params?: IncomeParams) {
  return requestClient.get<PageResult<IncomeModel>>(Api.incomeList, { params });
}

/**
 * 获取收入记录详情
 * @param id 收入记录ID
 * @returns 收入记录详情
 */
export function incomeInfo(id: ID) {
  return requestClient.get<IncomeModel>(`${Api.root}/${id}`);
}

/**
 * 新增收入记录
 * @param data 收入记录数据
 * @returns void
 */
export function incomeAdd(data: Partial<IncomeModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改收入记录
 * @param data 收入记录数据
 * @returns void
 */
export function incomeUpdate(data: Partial<IncomeModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除收入记录
 * @param ids 收入记录ID或ID数组
 * @returns void
 */
export function incomeRemove(ids: Array<ID> | ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${ids}`);
}

/**
 * 导出收入记录列表
 * @param params 查询参数
 * @returns void
 */
export function incomeExport(params?: IncomeParams) {
  return requestClient.download(Api.export, { params });
}

/**
 * 根据用户编号获取收入记录
 * @param userNo 用户编号
 * @returns 收入记录列表
 */
export function incomeByUserNo(userNo: string) {
  return requestClient.get<IncomeModel[]>(`${Api.incomeByUser}/${userNo}`);
}

/**
 * 获取收入记录统计信息
 * @param params 查询参数
 * @returns 收入记录统计信息
 */
export function incomeSummary(params?: IncomeParams) {
  return requestClient.get<WaterfeeIncomeSummaryVo>(Api.summary, { params });
}
