import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'masterUserId',
    label: '主用户ID',
    componentProps: {
      placeholder: '请输入主用户ID',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'masterUserNo',
    label: '主用户编号',
    componentProps: {
      placeholder: '请输入主用户编号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'attachedUserId',
    label: '附属用户ID',
    componentProps: {
      placeholder: '请输入附属用户ID',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'attachedUserNo',
    label: '附属用户编号',
    componentProps: {
      placeholder: '请输入附属用户编号',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'relationType',
    label: '关系类型',
    componentProps: {
      placeholder: '请选择关系类型',
      options: [
        { label: '单位-职工', value: 'COMPANY_EMPLOYEE' },
        { label: '业主-租户', value: 'OWNER_TENANT' },
        { label: '父子关系', value: 'PARENT_CHILD' },
        { label: '其他关系', value: 'OTHER' },
      ],
      allowClear: true,
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键',
    field: 'id',
  },
  {
    title: '主用户ID',
    field: 'masterUserId',
  },
  {
    title: '附属用户ID',
    field: 'attachedUserId',
  },
  {
    title: '关系类型',
    field: 'relationType',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'masterUserId',
    label: '主用户ID',
    rules: 'required',
    componentProps: {
      placeholder: '请输入主用户ID',
    },
  },
  {
    component: 'Input',
    fieldName: 'masterUserNo',
    label: '主用户编号',
    componentProps: {
      placeholder: '请输入主用户编号',
    },
  },
  {
    component: 'Input',
    fieldName: 'masterUserName',
    label: '主用户姓名',
    componentProps: {
      placeholder: '请输入主用户姓名',
    },
  },
  {
    component: 'Input',
    fieldName: 'attachedUserId',
    label: '附属用户ID',
    rules: 'required',
    componentProps: {
      placeholder: '请输入附属用户ID',
    },
  },
  {
    component: 'Input',
    fieldName: 'attachedUserNo',
    label: '附属用户编号',
    componentProps: {
      placeholder: '请输入附属用户编号',
    },
  },
  {
    component: 'Input',
    fieldName: 'attachedUserName',
    label: '附属用户姓名',
    componentProps: {
      placeholder: '请输入附属用户姓名',
    },
  },
  {
    component: 'Select',
    fieldName: 'relationType',
    label: '关系类型',
    rules: 'required',
    componentProps: {
      placeholder: '请选择关系类型',
      options: [
        { label: '单位-职工', value: 'COMPANY_EMPLOYEE' },
        { label: '业主-租户', value: 'OWNER_TENANT' },
        { label: '父子关系', value: 'PARENT_CHILD' },
        { label: '其他关系', value: 'OTHER' },
      ],
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'relationStartDate',
    label: '关系开始日期',
    componentProps: {
      placeholder: '请选择关系开始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    component: 'DatePicker',
    fieldName: 'relationEndDate',
    label: '关系结束日期',
    componentProps: {
      placeholder: '请选择关系结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '关系状态',
    componentProps: {
      placeholder: '请选择关系状态',
      options: [
        { label: '有效', value: 'ACTIVE' },
        { label: '无效', value: 'INACTIVE' },
        { label: '暂停', value: 'SUSPENDED' },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'operator',
    label: '操作员',
    componentProps: {
      placeholder: '请输入操作员',
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
];
