import type { ChargeManageArSplitVO, ChargeManageArSplitForm, ChargeManageArSplitQuery } from '#/api/waterfee/chargeManageArSplit/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/chargeManageArSplit',
  list = '/waterfee/chargeManageArSplit/list'
}

/**
 * 应收账分账记录导出
 * @param data data
 * @returns void
 */
export function ChargeManageArSplitExport(data: Partial<ChargeManageArSplitForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询应收账分账记录列表
 * @param params 查询参数
 * @returns 应收账分账记录列表
 */
export function listChargeManageArSplit(params?: ChargeManageArSplitQuery & PageQuery) {
  return requestClient.get<ChargeManageArSplitVO>(Api.list, { params });
}

/**
 * 查询应收账分账记录详细
 * @param id 应收账分账记录ID
 * @returns 应收账分账记录信息
 */
export function getChargeManageArSplit(id: string | number) {
  return requestClient.get<ChargeManageArSplitForm>(`${Api.root}/${id}`);
}

/**
 * 新增应收账分账记录
 * @param data 新增数据
 * @returns void
 */
export function addChargeManageArSplit(data: ChargeManageArSplitForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改应收账分账记录
 * @param data 修改数据
 * @returns void
 */
export function updateChargeManageArSplit(data: ChargeManageArSplitForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除应收账分账记录
 * @param id 应收账分账记录ID或ID数组
 * @returns void
 */
export function delChargeManageArSplit(id: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
