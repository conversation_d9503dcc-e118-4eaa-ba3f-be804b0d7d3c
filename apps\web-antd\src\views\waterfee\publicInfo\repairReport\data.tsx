import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'reporterName',
    label: '报修人姓名',
  },
  {
    component: 'Input',
    fieldName: 'contactPhone',
    label: '联系电话',
  },
  {
    component: 'DatePicker',
    fieldName: 'reportTime',
    label: '报修时间',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: 'ID',
  //   field: 'repairId',
  // },
  {
    title: '上报问题',
    field: 'reportContent',
  },
  {
    title: '报修人姓名',
    field: 'reporterName',
  },
  {
    title: '联系电话',
    field: 'contactPhone',
  },
  {
    title: '报修时间',
    field: 'reportTime',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'repairId',
    label: '主键',
  },
  {
    component: 'RichTextarea',
    componentProps: {
      width: '100%',
    },
    fieldName: 'reportContent',
    label: '上报问题',
  },
  {
    component: 'Input',
    fieldName: 'reporterName',
    label: '报修人姓名',
  },
  {
    component: 'Input',
    fieldName: 'contactPhone',
    label: '联系电话',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'reportTime',
    label: '报修时间',
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
  },
];
