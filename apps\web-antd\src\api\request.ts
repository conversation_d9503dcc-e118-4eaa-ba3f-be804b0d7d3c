/**
 * 该文件可自行根据业务逻辑进行调整
 */

import type { HttpResponse } from '@vben/request';

import { useAppConfig } from '@vben/hooks';
import { $t } from '@vben/locales';
import { preferences } from '@vben/preferences';
import {
  authenticateResponseInterceptor,
  errorMessageResponseInterceptor,
  RequestClient,
  stringify,
} from '@vben/request';
import { useAccessStore } from '@vben/stores';

import { message, Modal } from 'ant-design-vue';
import { isEmpty, isNull } from 'lodash-es';

import { useAuthStore } from '#/store';
import {
  decryptBase64,
  decryptWithAes,
  encryptBase64,
  encryptWithAes,
  generateAesKey,
} from '#/utils/encryption/crypto';
import * as encryptUtil from '#/utils/encryption/jsencrypt';

import { parseWithBigInt } from '../utils/json-bigint';

const { apiURL, clientId, enableEncrypt } = useAppConfig(
  import.meta.env,
  import.meta.env.PROD,
);

/**
 * 是否已经处在登出过程中了 一个标志位
 * 主要是防止一个页面会请求多个api 都401 会导致登出执行多次
 */
let isLogoutProcessing = false;

// 保存原始JSON.parse方法，防止无限递归
const originalJSONParse = JSON.parse;

// 重载JSON.parse防止递归调用
JSON.parse = function (text, reviver) {
  // 检查调用栈
  const stackTrace = new Error().stack || '';
  // 如果调用来自parseWithBigInt或已经在处理中，使用原始解析器
  if (stackTrace.includes('parseWithBigInt')) {
    return originalJSONParse(text, reviver);
  }

  // 尝试检测text中是否包含大整数模式
  if (typeof text === 'string' && /\d{16,}/.test(text)) {
    try {
      // console.log('检测到可能包含大整数的JSON');
      return parseWithBigInt(text);
    } catch (error) {
      console.error('大整数JSON解析失败，回退到原始解析器', error);
      return originalJSONParse(text, reviver);
    }
  }
  return originalJSONParse(text, reviver);
};

function createRequestClient(baseURL: string) {
  const client = new RequestClient({
    // 后端地址
    baseURL,
    // 消息提示类型
    errorMessageMode: 'message',
    // 是否返回原生响应 比如：需要获取响应头时使用该属性
    isReturnNativeResponse: false,
    // 需要对返回数据进行处理
    isTransformResponse: true,
    // 自定义Axios选项
    axiosOptions: {
      // 重写transformResponse以保留大整数精度
      transformResponse: [
        (data) => {
          if (typeof data === 'string' && data.length > 0) {
            try {
              // 显式使用我们的解析器
              console.log('处理API响应数据，内容长度:', data.length);
              const result = parseWithBigInt(data);
              return result;
            } catch (error) {
              // 如果解析失败，返回原始数据
              console.error('API响应解析失败:', error);
              return data;
            }
          }
          return data;
        },
      ],
    },
  });

  /**
   * 重新认证逻辑
   */
  async function doReAuthenticate() {
    console.warn('Access token or refresh token is invalid or expired. ');
    const accessStore = useAccessStore();
    const authStore = useAuthStore();
    accessStore.setAccessToken(null);
    if (
      preferences.app.loginExpiredMode === 'modal' &&
      accessStore.isAccessChecked
    ) {
      accessStore.setLoginExpired(true);
    } else {
      await authStore.logout();
    }
  }

  /**
   * 刷新token逻辑
   */
  async function doRefreshToken() {
    // 不需要
    // 保留此方法只是为了合并方便
    return '';
  }

  function formatToken(token: null | string) {
    return token ? `Bearer ${token}` : null;
  }

  client.addRequestInterceptor({
    fulfilled: (config) => {
      const accessStore = useAccessStore();
      // 添加token
      config.headers.Authorization = formatToken(accessStore.accessToken);
      /**
       * locale跟后台不一致 需要转换
       */
      const language = preferences.app.locale.replace('-', '_');
      config.headers['Accept-Language'] = language;
      config.headers['Content-Language'] = language;
      // 添加全局clientId
      config.headers.clientId = clientId;

      // 只有不是 FormData 时才设置 Content-Type
      if (config.data instanceof FormData) {
        // 是 FormData 时，删除 Content-Type，让 axios 自动处理
        if (config.headers['Content-Type']) {
          delete config.headers['Content-Type'];
        }
      } else {
        config.headers['Content-Type'] = 'application/json;charset=utf-8';
      }

      /**
       * 格式化get/delete参数
       * 如果包含自定义的paramsSerializer则不走此逻辑
       */
      if (
        ['DELETE', 'GET'].includes(config.method?.toUpperCase() || '') &&
        config.params &&
        !config.paramsSerializer
      ) {
        /**
         * 1. 格式化参数 微服务在传递区间时间选择(后端的params Map类型参数)需要格式化key 否则接收不到
         * 2. 数组参数需要格式化 后端才能正常接收 会变成arr=1&arr=2&arr=3的格式来接收
         */
        config.paramsSerializer = (params) =>
          stringify(params, { arrayFormat: 'repeat' });
      }

      const { encrypt } = config;
      // 全局开启请求加密功能 && 该请求开启 && 是post/put请求
      if (
        enableEncrypt &&
        encrypt &&
        ['POST', 'PUT'].includes(config.method?.toUpperCase() || '')
      ) {
        const aesKey = generateAesKey();
        config.headers['encrypt-key'] = encryptUtil.encrypt(
          encryptBase64(aesKey),
        );

        config.data =
          typeof config.data === 'object'
            ? encryptWithAes(JSON.stringify(config.data), aesKey)
            : encryptWithAes(config.data, aesKey);
      }

      // 处理查询参数
      if (config.params) {
        config.params = processRequestData(config.params);
      }

      // 处理请求体
      if (config.data && !(config.data instanceof FormData)) {
        config.data = processRequestData(config.data);
      }

      return config;
    },
  });

  // 通用的错误处理, 如果没有进入上面的错误处理逻辑，就会进入这里
  // 主要处理http状态码不为200(如网络异常/离线)的情况 必须放在在下面的响应拦截器之前
  client.addResponseInterceptor(
    errorMessageResponseInterceptor((msg: string) => message.error(msg)),
  );

  // 添加JSON解析器，处理大整数精度
  function parseJSONPreserveNumbers(jsonString: string) {
    return parseWithBigInt(jsonString);
  }

  client.addResponseInterceptor<HttpResponse>({
    fulfilled: async (response) => {
      const encryptKey = (response.headers ?? {})['encrypt-key'];
      if (encryptKey) {
        /** RSA私钥解密 拿到解密秘钥的base64 */
        const base64Str = encryptUtil.decrypt(encryptKey);
        /** base64 解码 得到请求头的 AES 秘钥 */
        const aesSecret = decryptBase64(base64Str.toString());
        /** 使用aesKey解密 responseData */
        const decryptData = decryptWithAes(
          response.data as unknown as string,
          aesSecret,
        );
        /** 赋值 需要转为对象 */
        response.data = JSON.parse(decryptData);
      }

      const { isReturnNativeResponse, isTransformResponse } = response.config;
      // 是否返回原生响应 比如：需要获取响应时使用该属性
      if (isReturnNativeResponse) {
        return response;
      }
      // 不进行任何处理，直接返回
      // 用于页面代码可能需要直接获取code，data，message这些信息时开启
      if (!isTransformResponse) {
        /**
         * 需要判断下载二进制的情况 正常是返回二进制 报错会返回json
         * 当type为blob且content-type为application/json时 则判断已经下载出错
         */
        if (
          response.config.responseType === 'blob' &&
          response.headers['content-type']?.includes?.('application/json')
        ) {
          // 这时候的data为blob类型
          const blob = response.data as unknown as Blob;
          // 拿到字符串转json对象
          response.data = JSON.parse(await blob.text());
          // 然后按正常逻辑执行下面的代码(判断业务状态码)
        } else {
          // 其他情况 直接返回
          return response.data;
        }
      }

      const axiosResponseData = response.data;
      if (!axiosResponseData) {
        throw new Error($t('http.apiRequestFailed'));
      }

      // 后端并没有采用严格的{code, msg, data}模式
      const { code, data, msg, ...other } = axiosResponseData;

      // 业务状态码为200则请求成功
      const hasSuccess = Reflect.has(axiosResponseData, 'code') && code === 200;
      if (hasSuccess) {
        let successMsg = msg;

        if (isNull(successMsg) || isEmpty(successMsg)) {
          successMsg = $t(`http.operationSuccess`);
        }

        if (response.config.successMessageMode === 'modal') {
          Modal.success({
            content: successMsg,
            title: $t('http.successTip'),
          });
        } else if (response.config.successMessageMode === 'message') {
          message.success(successMsg);
        }
        // 分页情况下为code msg rows total 并没有data字段
        // 如果有data 直接返回data 没有data将剩余参数(...other)封装为data返回
        // 需要考虑data为null的情况(比如查询为空) 所以这里直接判断undefined
        if (data !== undefined) {
          return data;
        }
        // 没有data 将其他参数包装为data
        return other;
      }
      // 在此处根据自己项目的实际情况对不同的code执行不同的操作
      // 如果不希望中断当前请求，请return数据，否则直接抛出异常即可
      let timeoutMsg = '';
      switch (code) {
        case 401: {
          // 已经在登出过程中 不再执行
          if (isLogoutProcessing) {
            return;
          }
          isLogoutProcessing = true;
          const _msg = $t('http.loginTimeout');
          const userStore = useAuthStore();
          userStore.logout().finally(() => {
            message.error(_msg);
            isLogoutProcessing = false;
          });
          // 不再执行下面逻辑
          return;
        }
        default: {
          if (msg) {
            timeoutMsg = msg;
          }
        }
      }

      // errorMessageMode='modal'的时候会显示modal错误弹窗，而不是消息提示，用于一些比较重要的错误
      // errorMessageMode='none' 一般是调用时明确表示不希望自动弹出错误提示
      if (response.config.errorMessageMode === 'modal') {
        Modal.error({
          content: timeoutMsg,
          title: $t('http.errorTip'),
        });
      } else if (response.config.errorMessageMode === 'message') {
        message.error(timeoutMsg);
      }

      // 如果响应是JSON格式，替换默认解析
      if (
        response.data &&
        typeof response.data === 'string' &&
        response.headers['content-type']?.includes('application/json')
      ) {
        try {
          // 使用支持大整数的解析器替换原始数据
          response.data = parseWithBigInt(response.data);
        } catch (error) {
          console.error('Error parsing JSON with big integers:', error);
        }
      }

      throw new Error(timeoutMsg || $t('http.apiRequestFailed'));
    },
  });

  // token过期的处理
  client.addResponseInterceptor(
    authenticateResponseInterceptor({
      client,
      doReAuthenticate,
      doRefreshToken,
      enableRefreshToken: preferences.app.enableRefreshToken,
      formatToken,
    }),
  );

  return client;
}

export const requestClient = createRequestClient(apiURL);

export const baseRequestClient = new RequestClient({ baseURL: apiURL });

/**
 * 处理请求数据，保留大数字精度
 * @param data 请求数据
 * @returns 处理后的数据
 */
function processRequestData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }

  // 对数组进行处理
  if (Array.isArray(data)) {
    return data.map((item) => processRequestData(item));
  }

  // 对对象进行处理
  if (typeof data === 'object' && !Array.isArray(data)) {
    const result: any = {};
    for (const key in data) {
      // 处理以Id结尾的字段，确保是字符串类型
      result[key] =
        key.endsWith('Id') &&
        (typeof data[key] === 'number' || typeof data[key] === 'string')
          ? String(data[key])
          : processRequestData(data[key]);
    }
    return result;
  }

  // 基本类型直接返回
  return data;
}

// 自定义选项
function createAxiosOptions(axiosOptions: AxiosRequestConfig = {}) {
  // 在发送请求前对数据做特殊处理，解决大整数精度丢失问题
  const transformRequest = [
    (data: any, headers: any) => {
      // 如果请求体是对象，处理其中可能的大整数ID
      if (data && typeof data === 'object') {
        // 先转为JSON字符串后重新解析，保留字符串形式的数字
        return JSON.parse(JSON.stringify(data));
      }
      return data;
    },
    ...(axios.defaults.transformRequest as AxiosTransformer[]),
  ];

  return {
    // 请求将被发送的基础url
    baseURL: import.meta.env.VITE_GLOB_API_URL,
    // 超时时间
    timeout: TIMEOUT,
    // 选项
    headers: {
      'x-requested-with': 'XMLHttpRequest',
    },
    transformRequest,
    ...axiosOptions,
  };
}
