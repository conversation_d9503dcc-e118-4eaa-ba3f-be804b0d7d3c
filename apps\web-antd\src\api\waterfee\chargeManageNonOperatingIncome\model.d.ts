export interface ChargeManageNonOperatingIncomeVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 收入类型
   */
  incomeType: string;

  /**
   * 金额
   */
  amount: number;

  /**
   * 收入时间
   */
  incomeTime: string;

  /**
   * 
   */
  remark: string;

}

export interface ChargeManageNonOperatingIncomeForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 收入类型
   */
  incomeType?: string;

  /**
   * 金额
   */
  amount?: number;

  /**
   * 收入时间
   */
  incomeTime?: string;

  /**
   * 
   */
  remark?: string;

}

export interface ChargeManageNonOperatingIncomeQuery extends PageQuery {

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 收入类型
   */
  incomeType?: string;

  /**
   * 金额
   */
  amount?: number;

  /**
   * 收入时间
   */
  incomeTime?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



