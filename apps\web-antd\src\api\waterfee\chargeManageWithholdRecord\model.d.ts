export interface ChargeManageWithholdRecordVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 
   */
  userId: string | number;

  /**
   * 
   */
  amount: number;

  /**
   * 扣款时间
   */
  withholdTime: string;

  /**
   * 状态（PENDING、SUCCESS、FAILED）
   */
  status: string;

  /**
   * 
   */
  remark: string;

}

export interface ChargeManageWithholdRecordForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 
   */
  userId?: string | number;

  /**
   * 
   */
  amount?: number;

  /**
   * 扣款时间
   */
  withholdTime?: string;

  /**
   * 状态（PENDING、SUCCESS、FAILED）
   */
  status?: string;

  /**
   * 
   */
  remark?: string;

}

export interface ChargeManageWithholdRecordQuery extends PageQuery {

  /**
   * 
   */
  userId?: string | number;

  /**
   * 
   */
  amount?: number;

  /**
   * 扣款时间
   */
  withholdTime?: string;

  /**
   * 状态（PENDING、SUCCESS、FAILED）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



