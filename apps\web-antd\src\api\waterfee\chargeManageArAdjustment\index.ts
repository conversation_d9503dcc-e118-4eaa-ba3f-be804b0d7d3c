import type { ChargeManageArAdjustmentVO, ChargeManageArAdjustmentForm, ChargeManageArAdjustmentQuery } from '#/api/waterfee/chargeManageArAdjustment/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/chargeManageArAdjustment',
  list = '/waterfee/chargeManageArAdjustment/list'
}

/**
 * 应收账追补记录导出
 * @param data data
 * @returns void
 */
export function ChargeManageArAdjustmentExport(data: Partial<ChargeManageArAdjustmentForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询应收账追补记录列表
 * @param params 查询参数
 * @returns 应收账追补记录列表
 */
export function listChargeManageArAdjustment(params?: ChargeManageArAdjustmentQuery & PageQuery) {
  return requestClient.get<ChargeManageArAdjustmentVO>(Api.list, { params });
}

/**
 * 查询应收账追补记录详细
 * @param id 应收账追补记录ID
 * @returns 应收账追补记录信息
 */
export function getChargeManageArAdjustment(id: string | number) {
  return requestClient.get<ChargeManageArAdjustmentForm>(`${Api.root}/${id}`);
}

/**
 * 新增应收账追补记录
 * @param data 新增数据
 * @returns void
 */
export function addChargeManageArAdjustment(data: ChargeManageArAdjustmentForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改应收账追补记录
 * @param data 修改数据
 * @returns void
 */
export function updateChargeManageArAdjustment(data: ChargeManageArAdjustmentForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除应收账追补记录
 * @param id 应收账追补记录ID或ID数组
 * @returns void
 */
export function delChargeManageArAdjustment(id: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
