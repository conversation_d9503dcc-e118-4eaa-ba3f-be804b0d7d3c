<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';
import type { ChargeManagePenaltyAdjustmentForm } from '#/api/waterfee/chargeManagePenaltyAdjustment/model.d';

import { computed, ref } from 'vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { Form, FormItem } from 'ant-design-vue';
import { pick } from 'lodash-es';
import { getDictOptions } from '#/utils/dict';
import { drawerSchema } from './data';

import { addChargeManagePenaltyAdjustment, getChargeManagePenaltyAdjustment, updateChargeManagePenaltyAdjustment } from '#/api/waterfee/chargeManagePenaltyAdjustment';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
          class: 'w-full',
        },
        formItemClass: 'col-span-1',
  },
  layout: 'vertical',
  schema: drawerSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onClosed: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);
    const { id } = drawerApi.getData() as { id?: number | string };
    isUpdate.value = !!id;
    if (isUpdate.value && id) {
      const record = await getChargeManagePenaltyAdjustment(id);
      await formApi.setValues(record);
    }
    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
        if (!valid) {
          return;
        }
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(await formApi.getValues());
    await (isUpdate.value ? updateChargeManagePenaltyAdjustment(data) : addChargeManagePenaltyAdjustment(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[800px]">
      <BasicForm />
    </BasicDrawer>
</template>
