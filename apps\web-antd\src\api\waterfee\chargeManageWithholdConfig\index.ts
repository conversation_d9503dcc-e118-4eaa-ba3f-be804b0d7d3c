import type { ChargeManageWithholdConfigVO, ChargeManageWithholdConfigForm, ChargeManageWithholdConfigQuery } from '#/api/waterfee/chargeManageWithholdConfig/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/chargeManageWithholdConfig',
  list = '/waterfee/chargeManageWithholdConfig/list'
}

/**
 * 代扣配置信息导出
 * @param data data
 * @returns void
 */
export function ChargeManageWithholdConfigExport(data: Partial<ChargeManageWithholdConfigForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询代扣配置信息列表
 * @param params 查询参数
 * @returns 代扣配置信息列表
 */
export function listChargeManageWithholdConfig(params?: ChargeManageWithholdConfigQuery & PageQuery) {
  return requestClient.get<ChargeManageWithholdConfigVO>(Api.list, { params });
}

/**
 * 查询代扣配置信息详细
 * @param id 代扣配置信息ID
 * @returns 代扣配置信息信息
 */
export function getChargeManageWithholdConfig(id: string | number) {
  return requestClient.get<ChargeManageWithholdConfigForm>(`${Api.root}/${id}`);
}

/**
 * 新增代扣配置信息
 * @param data 新增数据
 * @returns void
 */
export function addChargeManageWithholdConfig(data: ChargeManageWithholdConfigForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改代扣配置信息
 * @param data 修改数据
 * @returns void
 */
export function updateChargeManageWithholdConfig(data: ChargeManageWithholdConfigForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除代扣配置信息
 * @param id 代扣配置信息ID或ID数组
 * @returns void
 */
export function delChargeManageWithholdConfig(id: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
