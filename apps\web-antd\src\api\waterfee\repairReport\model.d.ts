export interface RepairReportVO {
  /**
   * ID
   */
  repairId: number | string;

  /**
   * 上报问题
   */
  reportContent: string;

  /**
   * 报修人姓名
   */
  reporterName: string;

  /**
   * 联系电话
   */
  contactPhone: string;

  /**
   * 报修时间
   */
  reportTime: string;

  /**
   * 备注
   */
  remark: string;
}

export interface RepairReportForm extends BaseEntity {
  /**
   * ID
   */
  repairId?: number | string;

  /**
   * 上报问题
   */
  reportContent?: string;

  /**
   * 报修人姓名
   */
  reporterName?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 报修时间
   */
  reportTime?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface RepairReportQuery extends PageQuery {
  /**
   * 上报问题
   */
  reportContent?: string;

  /**
   * 报修人姓名
   */
  reporterName?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 报修时间
   */
  reportTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
