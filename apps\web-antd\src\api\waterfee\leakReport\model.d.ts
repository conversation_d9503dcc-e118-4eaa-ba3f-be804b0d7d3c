export interface LeakReportVO {
  /**
   * ID
   */
  reportId: number | string;

  /**
   * 上报人姓名
   */
  reporterName: string;

  /**
   * 上报人电话
   */
  reporterPhone: string;

  /**
   * 举报时间
   */
  reportTime: string;

  /**
   * 问题描述
   */
  description: string;

  /**
   * 附件
   */
  file: string;

  /**
   * 经度
   */
  lon: string;

  /**
   * 维度
   */
  lat: string;

  /**
   * 备注
   */
  remark: string;
}

export interface LeakReportForm extends BaseEntity {
  /**
   * ID
   */
  reportId?: number | string;

  /**
   * 上报人姓名
   */
  reporterName?: string;

  /**
   * 上报人电话
   */
  reporterPhone?: string;

  /**
   * 举报时间
   */
  reportTime?: string;

  /**
   * 问题描述
   */
  description?: string;

  /**
   * 附件
   */
  file?: string;

  /**
   * 经度
   */
  lon?: string;

  /**
   * 维度
   */
  lat?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface LeakReportQuery extends PageQuery {
  /**
   * 上报人姓名
   */
  reporterName?: string;

  /**
   * 上报人电话
   */
  reporterPhone?: string;

  /**
   * 举报时间
   */
  reportTime?: string;

  /**
   * 问题描述
   */
  description?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
