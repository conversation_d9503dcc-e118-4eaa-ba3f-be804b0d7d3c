import type { InvoiceListResult } from './model/invoiceModel';

import { requestClient } from '#/api/request';

// 获取发票列表
export function getInvoiceList(params: Record<string, any>) {
  return requestClient.get<InvoiceListResult>('/waterfee/invoiceRecord/list', {
    params,
  });
}

export interface RequestInvoiceParams {
  buyerName: string;
  buyerTaxNumber?: string;
  isEnterprise: boolean;
  billNumber: string;
  pushMode: string;
  buyerPhone?: string;
  email?: string;
  invoiceType: string;
  invoiceCode?: string;
  invoiceNum?: string;
  clerk?: string;
}

// 开票请求
export function requestInvoice(data: RequestInvoiceParams) {
  return requestClient.postWithMsg<void>(
    '/waterfee/nuonuoInvoice/requestInvoice',
    data,
  );
}

// 获取发票详情
export function getInvoiceDetail(invoiceId: number) {
  return requestClient.get<any>(`/waterfee/invoiceRecord/${invoiceId}`);
}

// 作废发票
export function cancelInvoice(invoiceId: number) {
  return requestClient.get<any>('/waterfee/nuonuoInvoice/invoiceCancellation', {
    params: { invoiceId },
  });
}

// 刷新发票状态
export function refreshInvoiceStatus(serialNo: string) {
  return requestClient.get<any>(
    '/waterfee/nuonuoInvoice/refreshInvoiceStatus',
    { params: { serialNo } },
  );
}

// 获取红冲蓝票号选项-发票列表无分页
export function redFlushNumOptions(params: { invoiceType: string }) {
  return requestClient.get<any[]>(`/waterfee/invoiceRecord/selectList`, {
    params,
  });
}
