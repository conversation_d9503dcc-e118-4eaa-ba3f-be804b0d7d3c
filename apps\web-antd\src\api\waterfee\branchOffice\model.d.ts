export interface BranchOfficeVO {
  /**
   * ID
   */
  branchId: number | string;

  /**
   * 网点名称
   */
  branchName: string;

  /**
   * 详细地址
   */
  address: string;

  /**
   * 联系电话
   */
  contactPhone: string;

  /**
   * 经度
   */
  lon: string;

  /**
   * 维度
   */
  lat: string;

  /**
   * 备注
   */
  remark: string;
}

export interface BranchOfficeForm extends BaseEntity {
  /**
   * ID
   */
  branchId?: number | string;

  /**
   * 网点名称
   */
  branchName?: string;

  /**
   * 详细地址
   */
  address?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 经度
   */
  lon?: string;

  /**
   * 纬度
   */
  lat?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface BranchOfficeQuery extends PageQuery {
  /**
   * 网点名称
   */
  branchName?: string;

  /**
   * 详细地址
   */
  address?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
