import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'branchName',
    label: '网点名称',
  },
  {
    component: 'Input',
    fieldName: 'address',
    label: '详细地址',
  },
  {
    component: 'Input',
    fieldName: 'contactPhone',
    label: '联系电话',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: 'ID',
  //   field: 'branchId',
  // },
  {
    title: '网点名称',
    field: 'branchName',
  },
  {
    title: '详细地址',
    field: 'address',
  },
  {
    title: '联系电话',
    field: 'contactPhone',
  },
  {
    title: '经度',
    field: 'lon',
  },
  {
    title: '纬度',
    field: 'lat',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'branchId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'branchName',
    label: '网点名称',
  },
  {
    component: 'Input',
    fieldName: 'address',
    label: '详细地址',
  },
  {
    component: 'Input',
    fieldName: 'contactPhone',
    label: '联系电话',
  },
  {
    component: 'Input',
    fieldName: 'lon',
    label: '经度',
  },
  {
    component: 'Input',
    fieldName: 'lat',
    label: '纬度',
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
  },
];
