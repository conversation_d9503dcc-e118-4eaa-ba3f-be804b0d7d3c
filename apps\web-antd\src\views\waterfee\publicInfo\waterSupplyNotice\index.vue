<script lang="ts" setup>
import { onMounted } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { Button, message, Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  publicInfoList,
  publicInfoPublish,
  publicInfoRemove,
} from '#/api/waterfee/publicInfo';

import { columns, querySchema } from './data';
import waterSupplyNoticeDrawer from './water-supply-notice-drawer.vue';

const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const params = {
          ...formValues,
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          infoType: '2', // 供水公告
        };
        return await publicInfoList(params);
      },
    },
  },
  id: 'waterfee-publicInfo-waterSupplyNotice-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 使用抽屉组件
const [WaterSupplyNoticeDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: waterSupplyNoticeDrawer,
});

// 处理新增
function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

// 处理编辑
function handleEdit(row) {
  // 确保ID是正确的格式
  const id = row.infoId || row.id;
  drawerApi.setData({ id });
  drawerApi.open();
}

// 处理发布
async function handlePublish(row) {
  try {
    // 确保ID是正确的格式
    const id = row.infoId || row.id;
    await publicInfoPublish(id);
    message.success('发布成功');
    tableApi.query();
  } catch (error) {
    console.error('发布失败:', error);
    message.error(`发布失败: ${error.message || '未知错误'}`);
  }
}

// 处理删除
async function handleDelete(row) {
  try {
    // 确保ID是正确的格式
    const id = row.infoId || row.id;
    await publicInfoRemove(id);
    message.success('删除成功');
    tableApi.query();
  } catch (error) {
    console.error('删除失败:', error);
    message.error(`删除失败: ${error.message || '未知错误'}`);
  }
}

// 组件挂载时初始化
onMounted(() => {
  tableApi.query();
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="供水公告管理">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleAdd">新增</Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleEdit(row)">编辑</Button>
          <Button
            v-if="row.status !== '1'"
            type="link"
            @click="handlePublish(row)"
          >
            发布
          </Button>
          <Popconfirm
            placement="left"
            title="确认删除?"
            @confirm="handleDelete(row)"
          >
            <Button type="link" danger @click.stop="">删除</Button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>

    <WaterSupplyNoticeDrawer @reload="tableApi.query()" />
  </Page>
</template>

<style scoped>
.drawer-content {
  padding: 0 12px;
}
</style>
