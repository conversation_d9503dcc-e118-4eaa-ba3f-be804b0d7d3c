export interface ChargeManageUserRelationVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 主用户ID
   */
  masterUserId: string | number;

  /**
   * 附属用户ID
   */
  attachedUserId: string | number;

  /**
   * 关系类型（单位-职工、业主-租户等）
   */
  relationType: string;

  /**
   * 
   */
  remark: string;

}

export interface ChargeManageUserRelationForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 主用户ID
   */
  masterUserId?: string | number;

  /**
   * 附属用户ID
   */
  attachedUserId?: string | number;

  /**
   * 关系类型（单位-职工、业主-租户等）
   */
  relationType?: string;

  /**
   * 
   */
  remark?: string;

}

export interface ChargeManageUserRelationQuery extends PageQuery {

  /**
   * 主用户ID
   */
  masterUserId?: string | number;

  /**
   * 附属用户ID
   */
  attachedUserId?: string | number;

  /**
   * 关系类型（单位-职工、业主-租户等）
   */
  relationType?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



