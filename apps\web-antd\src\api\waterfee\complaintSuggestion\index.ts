import type { PageQuery } from '#/api/common';
import type {
  ComplaintSuggestionForm,
  ComplaintSuggestionQuery,
  ComplaintSuggestionVO,
} from '#/api/waterfee/complaintSuggestion/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/complaintSuggestion/list',
  root = '/waterfee/complaintSuggestion',
}

/**
 * 投诉建议导出
 * @param data data
 * @returns void
 */
export function ComplaintSuggestionExport(
  data: Partial<ComplaintSuggestionForm>,
) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询投诉建议列表
 * @param params 查询参数
 * @returns 投诉建议列表
 */
export function listComplaintSuggestion(
  params?: ComplaintSuggestionQuery & PageQuery,
) {
  return requestClient.get<ComplaintSuggestionVO>(Api.list, { params });
}

/**
 * 查询投诉建议详细
 * @param complaintSuggestionId 投诉建议ID
 * @returns 投诉建议信息
 */
export function getComplaintSuggestion(complaintSuggestionId: number | string) {
  return requestClient.get<ComplaintSuggestionForm>(
    `${Api.root}/${complaintSuggestionId}`,
  );
}

/**
 * 新增投诉建议
 * @param data 新增数据
 * @returns void
 */
export function addComplaintSuggestion(data: ComplaintSuggestionForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改投诉建议
 * @param data 修改数据
 * @returns void
 */
export function updateComplaintSuggestion(data: ComplaintSuggestionForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除投诉建议
 * @param complaintSuggestionId 投诉建议ID或ID数组
 * @returns void
 */
export function delComplaintSuggestion(
  complaintSuggestionId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(
    `${Api.root}/${complaintSuggestionId}`,
  );
}
