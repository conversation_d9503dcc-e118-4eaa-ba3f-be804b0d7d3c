import type { ChargeManageThirdPartyVO, ChargeManageThirdPartyForm, ChargeManageThirdPartyQuery } from '#/api/waterfee/chargeManageThirdParty/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/chargeManageThirdParty',
  list = '/waterfee/chargeManageThirdParty/list'
}

/**
 * 第三方对账记录导出
 * @param data data
 * @returns void
 */
export function ChargeManageThirdPartyExport(data: Partial<ChargeManageThirdPartyForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询第三方对账记录列表
 * @param params 查询参数
 * @returns 第三方对账记录列表
 */
export function listChargeManageThirdParty(params?: ChargeManageThirdPartyQuery & PageQuery) {
  return requestClient.get<ChargeManageThirdPartyVO>(Api.list, { params });
}

/**
 * 查询第三方对账记录详细
 * @param id 第三方对账记录ID
 * @returns 第三方对账记录信息
 */
export function getChargeManageThirdParty(id: string | number) {
  return requestClient.get<ChargeManageThirdPartyForm>(`${Api.root}/${id}`);
}

/**
 * 新增第三方对账记录
 * @param data 新增数据
 * @returns void
 */
export function addChargeManageThirdParty(data: ChargeManageThirdPartyForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改第三方对账记录
 * @param data 修改数据
 * @returns void
 */
export function updateChargeManageThirdParty(data: ChargeManageThirdPartyForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除第三方对账记录
 * @param id 第三方对账记录ID或ID数组
 * @returns void
 */
export function delChargeManageThirdParty(id: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
