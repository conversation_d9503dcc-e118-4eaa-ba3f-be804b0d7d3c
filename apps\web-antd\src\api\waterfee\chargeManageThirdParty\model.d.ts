export interface ChargeManageThirdPartyVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 渠道编码（UNIONPAY、WECHAT等）
   */
  channelCode: string;

  /**
   * 本地订单号
   */
  localOrderNo: string;

  /**
   * 第三方订单号
   */
  thirdOrderNo: string;

  /**
   * 
   */
  amount: number;

  /**
   * 状态（MATCHED、MISMATCHED、PENDING）
   */
  status: string;

  /**
   * 对账日期
   */
  reconciliationDate: string;

  /**
   * 
   */
  remark: string;

}

export interface ChargeManageThirdPartyForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 渠道编码（UNIONPAY、WECHAT等）
   */
  channelCode?: string;

  /**
   * 本地订单号
   */
  localOrderNo?: string;

  /**
   * 第三方订单号
   */
  thirdOrderNo?: string;

  /**
   * 
   */
  amount?: number;

  /**
   * 状态（MATCHED、MISMATCHED、PENDING）
   */
  status?: string;

  /**
   * 对账日期
   */
  reconciliationDate?: string;

  /**
   * 
   */
  remark?: string;

}

export interface ChargeManageThirdPartyQuery extends PageQuery {

  /**
   * 渠道编码（UNIONPAY、WECHAT等）
   */
  channelCode?: string;

  /**
   * 本地订单号
   */
  localOrderNo?: string;

  /**
   * 第三方订单号
   */
  thirdOrderNo?: string;

  /**
   * 
   */
  amount?: number;

  /**
   * 状态（MATCHED、MISMATCHED、PENDING）
   */
  status?: string;

  /**
   * 对账日期
   */
  reconciliationDate?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



