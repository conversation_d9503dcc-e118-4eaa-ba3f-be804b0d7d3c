<script setup lang="ts">
import type { UserForm } from '#/api/waterfee/user/archivesManage/model';
import type { DescItem } from '#/components/description';

import { computed, h, watch } from 'vue';

import { Description, useDescription } from '#/components/description';
import { renderDict } from '#/utils/render';

import { flowSchema } from './data';

const props = defineProps<{ data: UserForm }>();

watch(
  () => props.data,
  (newData) => {
    setDescProps({ data: newData }, true);
  },
);

// 获取字典名称
const getDictName = (fieldName: string) => {
  const dictMap: Record<string, string> = {
    customerNature: 'waterfee_user_customer_nature',
    useWaterNature: 'waterfee_user_use_water_nature',
    userStatus: 'waterfee_user_user_status',
    auditStatus: 'audit_status',
    certificateType: 'waterfee_user_certificate_type',
    invoiceType: 'waterfee_user_invoice_type',
    billingMethod: 'waterfee_user_billing_method',
    ifPenalty: 'yes_no',
    ifExtraCharge: 'yes_no',
    penaltyType: 'waterfee_user_penalty_type',
    priceUseWaterNature: 'waterfee_user_use_water_nature',
  };
  return dictMap[fieldName] || '';
};

// 构建描述项
const descSchema = computed<DescItem[]>(() => {
  return flowSchema()
    .filter((item) => item.fieldName !== 'userId') // 过滤掉不需要显示的字段，userId是水表信息的字段
    .map((item) => {
      return {
        field: item.fieldName,
        label: item.label as string,
        span: 1,
        render: (val: any) => {
          const dictName = getDictName(item.fieldName);
          if (dictName) {
            return renderDict(val, dictName);
          }
          if (item.fieldName === 'supplyDate' && val) {
            return val;
          }
          if (item.fieldName === 'picture' && val) {
            // 以,拆分为url数组，然后渲染为图片，点击图片放大预览
            const urlList = val.split(',');
            return urlList.map((url: string) => {
              return h(
                'img',
                {
                  src: url,
                  style: 'max-width: 100px; max-height: 100px;',
                  onClick: () => {
                    window.open(url, '_blank');
                  },
                },
                url,
              );
            });
          }
          if (item.fieldName === 'file' && val) {
            // 以,拆分为url数组，然后渲染为下载链接
            const urlList = val.split(',');
            return urlList.map((url: string) => {
              return h(
                'div',
                { style: 'margin-bottom: 5px;' },
                h(
                  'a',
                  {
                    href: url,
                    target: '_blank',
                  },
                  url,
                ),
              );
            });
          }
          return val || '-';
        },
      } satisfies DescItem;
    });
});
// 使用Description组件
const [registerDescription, { setDescProps }] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: descSchema.value,
  data: props.data,
});
</script>

<template>
  <div class="p-4">
    <Description @register="registerDescription" />
  </div>
</template>
