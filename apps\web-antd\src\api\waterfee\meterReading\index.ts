import type { BasicPageParams, BasicFetchResult } from '#/api/types/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';
import { preserveBigInt } from '#/utils/json-bigint';

// 抄表任务API
enum Api {
  list = '/waterfee/readingTask/list',
  detail = '/waterfee/readingTask',
  add = '/waterfee/readingTask',
  update = '/waterfee/readingTask',
  delete = '/waterfee/readingTask',
  export = '/waterfee/readingTask/export',
  pause = '/waterfee/readingTask/pause',
  enable = '/waterfee/readingTask/enable',
  dispatch = '/waterfee/readingTask/dispatch',
  updateCount = '/waterfee/readingTask/updateCount',
  handleTestApi = '/waterfee/scheduler/test',
}

// 抄表任务模型
export interface ReadingTaskModel {
  taskId: string;
  taskName: string;
  businessAreaId: string;
  businessAreaName?: string;
  meterBookId: string;
  meterBookName?: string;
  readerId: string;
  readerName?: string;
  readingMethod: string;
  readingCycle: string;
  readingDay: number;
  baseDay: number;
  isCycle: string;
  taskStatus: string;
  startDate: string;
  endDate?: string;
  lastExecuteTime?: string;
  nextExecuteTime?: string;
  remark?: string;
  createTime?: string;
  [key: string]: any;
}

// 抄表任务查询参数
export interface ReadingTaskParams extends BasicPageParams {
  taskName?: string;
  businessAreaId?: string;
  meterBookId?: string;
  readerId?: string;
  readingMethod?: string;
  readingCycle?: string;
  taskStatus?: string;
  startDateRange?: [string, string];
  [key: string]: any;
}

// 获取抄表任务列表
export function getReadingTaskList(params?: ReadingTaskParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 处理日期范围
  if (
    queryParams.startDateRange &&
    Array.isArray(queryParams.startDateRange)
  ) {
    queryParams.startDateBegin = queryParams.startDateRange[0];
    queryParams.startDateEnd = queryParams.startDateRange[1];
    delete queryParams.startDateRange;
  }

  // 保留大整数精度
  const safeParams = preserveBigInt(queryParams);

  return requestClient.get<BasicFetchResult<ReadingTaskModel[]>>(
    Api.list,
    {
      params: safeParams,
    },
  );
}

// 获取抄表任务详情
export function getReadingTaskInfo(taskId: string) {
  return requestClient.get<ReadingTaskModel>(
    `${Api.detail}/${taskId}`,
  );
}

// 添加抄表任务
export function addReadingTask(data: ReadingTaskModel) {
  return requestClient.post(Api.add, preserveBigInt(data));
}

// 更新抄表任务
export function updateReadingTask(data: ReadingTaskModel) {
  return requestClient.put(Api.update, preserveBigInt(data));
}

// 删除抄表任务
export function deleteReadingTask(taskIds: string | string[]) {
  return requestClient.delete(`${Api.delete}/${taskIds}`);
}

// 暂停抄表任务
export function pauseReadingTask(taskId: string) {
  return requestClient.put(`${Api.pause}/${taskId}`);
}

// 启用抄表任务
export function enableReadingTask(taskId: string) {
  return requestClient.put(`${Api.enable}/${taskId}`);
}

// 下发抄表任务
export function dispatchReadingTask(taskId: string) {
  return requestClient.put(`${Api.dispatch}/${taskId}`);
}

// 导出抄表任务
export function exportReadingTask(params?: ReadingTaskParams) {
  // 确保params是一个对象，防止undefined导致错误
  const safeParams = params ? preserveBigInt(params) : {};
  return commonExport(Api.export, safeParams);
}

// 更新表册关联用户数和水表数
export function updateReadingTaskCount(taskId: string) {
  return requestClient.put(`${Api.updateCount}/${taskId}`);
}

// 批量更新表册关联用户数和水表数
export function updateReadingTaskCountBatch(taskIds: string[]) {
  return requestClient.put(`${Api.updateCount}/batch/${taskIds.join(',')}`);
}

// 测试任务
export function handleTestApi(taskId: string) {
  return requestClient.get(
    `${Api.handleTestApi}`,
  );
}
