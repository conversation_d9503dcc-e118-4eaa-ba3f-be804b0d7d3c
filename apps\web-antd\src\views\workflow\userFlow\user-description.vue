<script setup lang="ts">
// import type { LeaveVO } from './api/model';
// import type { UserVO } from '#/api/waterfee/user/archivesManage/model';
import type { DescItem } from '#/components/description';

import { computed, onMounted, ref } from 'vue';

import { listLadderPrice } from '#/api/waterfee/price/ladderPrice';
import { Description, useDescription } from '#/components/description';
import { renderDict } from '#/utils/render';

import { flowSchema, meterDescSchema, priceDescSchema } from './data';

// import dayjs from 'dayjs';

defineOptions({
  name: 'UserDescription',
  inheritAttrs: false,
});

const props = defineProps<{ data: any }>();

// 获取字典名称
const getDictName = (fieldName: string) => {
  const dictMap: Record<string, string> = {
    customerNature: 'waterfee_user_customer_nature',
    useWaterNature: 'waterfee_user_use_water_nature',
    userStatus: 'waterfee_user_user_status',
    auditStatus: 'audit_status',
    certificateType: 'waterfee_user_certificate_type',
    invoiceType: 'waterfee_user_invoice_type',
    billingMethod: 'waterfee_user_billing_method',
    ifPenalty: 'yes_no',
    ifExtraCharge: 'yes_no',
    penaltyType: 'waterfee_user_penalty_type',
    priceUseWaterNature: 'waterfee_user_use_water_nature',
  };
  return dictMap[fieldName] || '';
};

// 构建描述项
const descSchema = computed<DescItem[]>(() => {
  return flowSchema()
    .filter((item) => item.fieldName !== 'userId') // 过滤掉不需要显示的字段
    .map((item) => {
      return {
        field: item.fieldName,
        label: item.label as string,
        span: 1,
        render: (val: any) => {
          const dictName = getDictName(item.fieldName);
          if (dictName) {
            return renderDict(val, dictName);
          }
          if (item.fieldName === 'supplyDate' && val) {
            return val;
          }
          return val || '-';
        },
      } satisfies DescItem;
    });
});
// 创建一个映射表
const ladderPriceMap = ref<Record<string, string>>({});

// 加载计费方式数据
const loadLadderPriceData = async () => {
  try {
    const res = await listLadderPrice({
      pageNum: 1,
      pageSize: 9999,
    });

    // 构建ID到名称的映射
    const map: Record<string, string> = {};
    res.rows.forEach((item) => {
      // 处理 name 可能为 undefined 的情况
      map[item.id] = item.name || '未命名';
    });

    ladderPriceMap.value = map;
  } catch (error) {
    console.error('获取计费方式数据失败:', error);
  }
};
// 修改schema中的render函数
const modifiedSchema = priceDescSchema.map((item) => {
  if (item.field === 'billingMethod') {
    return {
      ...item,
      render: (val: any) => {
        if (!val) return '-';
        return ladderPriceMap.value[val] || val;
      },
    };
  }
  return item;
});

// 使用Description组件
const [registerDescription] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: descSchema.value,
  data: props.data.userBasicInfo,
});
const [meterDescription] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: meterDescSchema,
  data: props.data.waterfeeMeterBo,
});
const [priceDescription] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: modifiedSchema,
  data: {
    ...props.data.userPrice,
    extraChargeType: props.data.userPrice.extraChargeType
      ? props.data.userPrice.extraChargeType.split(',')
      : [],
  },
});

// 在组件挂载时加载计费方式数据
onMounted(() => {
  loadLadderPriceData();
});
</script>

<template>
  <div>
    <h3>基本信息</h3>
    <Description @register="registerDescription" />

    <h3 class="mt-4">水表信息</h3>
    <Description @register="meterDescription" />

    <h3 class="mt-4">价格信息</h3>
    <Description @register="priceDescription" />
  </div>
</template>

<style scoped>
h3 {
  padding-left: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  border-left: 3px solid #1890ff;
}
</style>
