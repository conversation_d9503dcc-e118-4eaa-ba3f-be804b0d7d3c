export interface ChargeManageArSplitVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 账单ID
   */
  billId: string | number;

  /**
   * 项目编码
   */
  projectCode: string;

  /**
   * 部门ID
   */
  departmentId: string | number;

  /**
   * 分账金额
   */
  splitAmount: number;

  /**
   * 
   */
  remark: string;

}

export interface ChargeManageArSplitForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 账单ID
   */
  billId?: string | number;

  /**
   * 项目编码
   */
  projectCode?: string;

  /**
   * 部门ID
   */
  departmentId?: string | number;

  /**
   * 分账金额
   */
  splitAmount?: number;

  /**
   * 
   */
  remark?: string;

}

export interface ChargeManageArSplitQuery extends PageQuery {

  /**
   * 账单ID
   */
  billId?: string | number;

  /**
   * 项目编码
   */
  projectCode?: string;

  /**
   * 部门ID
   */
  departmentId?: string | number;

  /**
   * 分账金额
   */
  splitAmount?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



