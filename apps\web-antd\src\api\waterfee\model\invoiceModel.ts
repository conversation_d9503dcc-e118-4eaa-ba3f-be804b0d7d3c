export interface InvoiceItem {
  /** 发票ID */
  invoiceId: number;
  /** 关联账单编号 */
  billNumber: string;
  /** 发票平台请求流水号 */
  serialNo: string;
  /** 开票状态(process, success, failure, cancel) */
  invoiceStatus: 'cancel' | 'failure' | 'process' | 'success';
  /** 失败原因 */
  failCause?: string;
  /** 发票pdf地址 */
  pdfUrl?: string;
  /** 开票时间 */
  invoiceTime: string;
  /** 发票代码 */
  invoiceCode?: string;
  /** 发票号码 */
  invoiceNo?: string;
  /** 发票类型 1:蓝票;2:红票 */
  invoiceType: '1' | '2';
  /** 发票种类 */
  invoiceKind?: string;
  /** 价税合计 */
  orderAmount: string;
  /** 不含税金额 */
  exTaxAmount: string;
  /** 含税金额 */
  taxAmount: string;
}

export interface InvoiceListResult {
  total: number;
  list: InvoiceItem[];
}
