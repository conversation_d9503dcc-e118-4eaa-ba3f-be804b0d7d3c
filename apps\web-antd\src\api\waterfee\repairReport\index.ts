import type { PageQuery } from '#/api/common';
import type {
  RepairReportForm,
  RepairReportQuery,
  RepairReportVO,
} from '#/api/waterfee/repairReport/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/repairReport/list',
  root = '/waterfee/repairReport',
}

/**
 * 报修管理导出
 * @param data data
 * @returns void
 */
export function RepairReportExport(data: Partial<RepairReportForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询报修管理列表
 * @param params 查询参数
 * @returns 报修管理列表
 */
export function listRepairReport(params?: PageQuery & RepairReportQuery) {
  return requestClient.get<RepairReportVO>(Api.list, { params });
}

/**
 * 查询报修管理详细
 * @param repairId 报修管理ID
 * @returns 报修管理信息
 */
export function getRepairReport(repairId: number | string) {
  return requestClient.get<RepairReportForm>(`${Api.root}/${repairId}`);
}

/**
 * 新增报修管理
 * @param data 新增数据
 * @returns void
 */
export function addRepairReport(data: RepairReportForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改报修管理
 * @param data 修改数据
 * @returns void
 */
export function updateRepairReport(data: RepairReportForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除报修管理
 * @param repairId 报修管理ID或ID数组
 * @returns void
 */
export function delRepairReport(
  repairId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${repairId}`);
}
