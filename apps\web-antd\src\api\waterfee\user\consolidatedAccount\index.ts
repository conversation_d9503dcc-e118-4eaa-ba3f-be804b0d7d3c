import type { PageQuery } from '#/api/common';
import type {
  ConsolidatedAccountForm,
  ConsolidatedAccountQuery,
  ConsolidatedAccountVO,
} from '#/api/waterfee/user/consolidatedAccount/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/consolidatedAccount/list',
  root = '/waterfee/consolidatedAccount',
}

/**
 * 合收户管理导出
 * @param data data
 * @returns void
 */
export function ConsolidatedAccountExport(
  data: Partial<ConsolidatedAccountForm>,
) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询合收户管理列表
 * @param params 查询参数
 * @returns 合收户管理列表
 */
export function listConsolidatedAccount(
  params?: ConsolidatedAccountQuery & PageQuery,
) {
  return requestClient.get<ConsolidatedAccountVO>(Api.list, { params });
}

/**
 * 查询合收户管理详细
 * @param consolidatedAccountId 合收户管理ID
 * @returns 合收户管理信息
 */
export function getConsolidatedAccount(consolidatedAccountId: number | string) {
  return requestClient.get<ConsolidatedAccountForm>(
    `${Api.root}/${consolidatedAccountId}`,
  );
}

/**
 * 新增合收户管理
 * @param data 新增数据
 * @returns void
 */
export function addConsolidatedAccount(data: ConsolidatedAccountForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改合收户管理
 * @param data 修改数据
 * @returns void
 */
export function updateConsolidatedAccount(data: ConsolidatedAccountForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除合收户管理
 * @param consolidatedAccountId 合收户管理ID或ID数组
 * @returns void
 */
export function delConsolidatedAccount(
  consolidatedAccountId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(
    `${Api.root}/${consolidatedAccountId}`,
  );
}
