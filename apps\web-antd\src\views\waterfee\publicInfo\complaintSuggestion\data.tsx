import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'submitterName',
    label: '提交人姓名',
  },
  {
    component: 'Input',
    fieldName: 'contactPhone',
    label: '联系电话',
  },
  {
    component: 'DatePicker',
    fieldName: 'submitTime',
    label: '投诉时间',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: 'ID',
  //   field: 'complaintSuggestionId',
  // },
  {
    title: '提交人姓名',
    field: 'submitterName',
  },
  {
    title: '联系电话',
    field: 'contactPhone',
  },
  {
    title: '投诉内容',
    field: 'content',
  },
  {
    title: '投诉时间',
    field: 'submitTime',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'complaintSuggestionId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'submitterName',
    label: '提交人姓名',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'contactPhone',
    label: '联系电话',
  },
  {
    component: 'RichTextarea',
    componentProps: {
      width: '100%',
    },
    fieldName: 'content',
    label: '投诉内容',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'submitTime',
    label: '投诉时间',
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
  },
];
