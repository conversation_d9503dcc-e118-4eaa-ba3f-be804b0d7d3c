import type { FormSchemaGetter } from '#/adapter/form';
import type { TableColumnType } from 'ant-design-vue';
import { getPopupContainer } from '@vben/utils';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 查询表单模式
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '用户编号',
    componentProps: {
      placeholder: '请输入用户编号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用户姓名',
    componentProps: {
      placeholder: '请输入用户姓名',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'phoneNumber',
    label: '手机号码',
    componentProps: {
      placeholder: '请输入手机号码',
      allowClear: true,
    },
  },
];

// 账单表格列定义
export const billColumns: TableColumnType[] = [
  {
    title: '账单编号',
    dataIndex: 'billNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '用户编号',
    dataIndex: 'userNo',
    width: 120,
    align: 'center',
  },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    width: 120,
    align: 'center',
  },
  {
    title: '用水地址',
    dataIndex: 'address',
    width: 200,
    align: 'center',
  },
  {
    title: '计费周期',
    dataIndex: 'billingPeriod',
    width: 200,
    align: 'center',
    customRender: ({ record }) => {
      if (record.billingPeriodStart && record.billingPeriodEnd) {
        return `${record.billingPeriodStart.substring(0, 10)} 至 ${record.billingPeriodEnd.substring(0, 10)}`;
      }
      return '';
    },
  },
  {
    title: '用水量(m³)',
    dataIndex: 'consumptionVolume',
    width: 120,
    align: 'center',
  },
  {
    title: '账单金额(元)',
    dataIndex: 'totalAmount',
    width: 120,
    align: 'center',
  },
  {
    title: '已付金额(元)',
    dataIndex: 'amountPaid',
    width: 120,
    align: 'center',
  },
  {
    title: '应付金额(元)',
    dataIndex: 'balanceDue',
    width: 120,
    align: 'center',
  },
  {
    title: '账单状态',
    dataIndex: 'billStatus',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      let color = 'blue';
      let label = '未知';

      switch (text) {
        case 'DRAFT':
          color = 'default';
          label = '草稿';
          break;
        case 'ISSUED':
          color = 'processing';
          label = '已发行';
          break;
        case 'PAID':
          color = 'success';
          label = '已支付';
          break;
        case 'OVERDUE':
          color = 'error';
          label = '已逾期';
          break;
        case 'CANCELLED':
          color = 'warning';
          label = '已取消';
          break;
      }

      return h(Tag, { color }, () => label);
    },
  },
];

// 带操作列的账单表格列定义（用于窗口收费）
export const billColumnsWithActions: TableColumnType[] = [
  ...billColumns,
  {
    title: '操作',
    key: 'action',
    width: 100,
    align: 'center',
    fixed: 'right',
  },
];

// 缴费明细表格列定义
export const paymentDetailColumns: TableColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1,
  },
  {
    title: '缴费编号',
    dataIndex: 'paymentDetailId',
    width: 150,
    align: 'center',
  },
  {
    title: '账单编号',
    dataIndex: 'billNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '缴费金额(元)',
    dataIndex: 'paymentAmount',
    width: 120,
    align: 'center',
  },
  {
    title: '缴费方式',
    dataIndex: 'paymentMethod',
    width: 120,
    align: 'center',
  },
  {
    title: '缴费时间',
    dataIndex: 'paymentTime',
    width: 180,
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'createBy',
    width: 120,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    align: 'center',
  },
];

// 抄表计费表格列定义
export const meterReadingColumns: TableColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1,
  },
  {
    title: '水表编号',
    dataIndex: 'meterNo',
    width: 120,
    align: 'center',
  },
  {
    title: '抄表日期',
    dataIndex: 'readingDate',
    width: 150,
    align: 'center',
  },
  {
    title: '上期读数',
    dataIndex: 'previousReadingValue',
    width: 120,
    align: 'center',
  },
  {
    title: '本期读数',
    dataIndex: 'currentReadingValue',
    width: 120,
    align: 'center',
  },
  {
    title: '用水量(m³)',
    dataIndex: 'consumptionVolume',
    width: 120,
    align: 'center',
  },
  {
    title: '抄表方式',
    dataIndex: 'readingMethod',
    width: 120,
    align: 'center',
  },
  {
    title: '抄表人',
    dataIndex: 'meterReader',
    width: 120,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    align: 'center',
  },
];

// 支付方式选项
export const paymentMethodOptions = [
  { label: '现金支付', value: 'CASH' },
  { label: '银行卡支付', value: 'BANK_CARD' },
  { label: '微信支付', value: 'WECHAT' },
  { label: '支付宝支付', value: 'ALIPAY' },
  { label: '预存款支付', value: 'DEPOSIT' },
];

// 退款方式选项
export const refundMethodOptions = [
  { label: '现金退款', value: 'CASH' },
  { label: '银行卡退款', value: 'BANK_CARD' }, 
];
