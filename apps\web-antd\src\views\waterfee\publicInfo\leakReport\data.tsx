import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'reporterName',
    label: '上报人姓名',
  },
  {
    component: 'Input',
    fieldName: 'reporterPhone',
    label: '上报人电话',
  },
  {
    component: 'DatePicker',
    fieldName: 'reportTime',
    label: '举报时间',
  },
  {
    component: 'Input',
    fieldName: 'description',
    label: '问题描述',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: 'ID',
  //   field: 'reportId',
  // },
  {
    title: '上报人姓名',
    field: 'reporterName',
  },
  {
    title: '上报人电话',
    field: 'reporterPhone',
  },
  {
    title: '举报时间',
    field: 'reportTime',
  },
  {
    title: '问题描述',
    field: 'description',
  },
  {
    title: '附件',
    field: 'file',
  },
  {
    title: '经度',
    field: 'lon',
  },
  {
    title: '维度',
    field: 'lat',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'reportId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'reporterName',
    label: '上报人姓名',
  },
  {
    component: 'Input',
    fieldName: 'reporterPhone',
    label: '上报人电话',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'reportTime',
    label: '举报时间',
  },
  {
    component: 'Textarea',
    fieldName: 'description',
    label: '问题描述',
  },
  {
    component: 'Upload',
    fieldName: 'file',
    label: '附件',
  },
  {
    component: 'Input',
    fieldName: 'lon',
    label: '经度',
  },
  {
    component: 'Input',
    fieldName: 'lat',
    label: '维度',
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
  },
];
