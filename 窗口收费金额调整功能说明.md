# 窗口收费金额调整功能实现说明

## 功能概述
为窗口收费的代收费账单添加了金额调整按钮，允许操作员对账单金额进行调整，支持增加或减少金额，并记录调整原因。

## 实现内容

### 1. API接口集成
**文件**: `apps/web-antd/src/views/waterfee/bill/counter-payment/index.vue`

- 导入 `billAdjustConsumption` 接口
- 使用现有的 `/waterfee/bill/adjust` 接口
- 支持调整金额和调整原因参数

### 2. 表格列定义扩展
**文件**: `apps/web-antd/src/views/waterfee/bill/counter-payment/data.ts`

#### 新增带操作列的表格定义
```typescript
export const billColumnsWithActions: TableColumnType[] = [
  ...billColumns,
  {
    title: '操作',
    key: 'action',
    width: 100,
    align: 'center',
    fixed: 'right',
  },
];
```

### 3. 前端状态管理
**文件**: `apps/web-antd/src/views/waterfee/bill/counter-payment/index.vue`

#### 新增状态变量
```javascript
// 金额调整相关状态
const billAdjustModalVisible = ref(false);
const currentAdjustBill = ref(null);
const billAdjustForm = reactive({
  adjustmentsAmount: 0,
  adjustmentReason: '',
});
```

### 4. 核心功能函数

#### 打开调整弹窗
```javascript
function openBillAdjustModal(record) {
  currentAdjustBill.value = record;
  billAdjustForm.adjustmentsAmount = record.adjustmentsAmount || 0;
  billAdjustForm.adjustmentReason = '';
  billAdjustModalVisible.value = true;
}
```

#### 确认金额调整
```javascript
async function confirmBillAdjust() {
  // 验证调整原因
  if (!billAdjustForm.adjustmentReason || billAdjustForm.adjustmentReason.trim() === '') {
    message.warning('请输入调整原因');
    return;
  }

  try {
    // 调用调整接口
    await billAdjustConsumption({
      billId: currentAdjustBill.value.billId,
      adjustmentsAmount: Number(billAdjustForm.adjustmentsAmount),
      adjustmentReason: billAdjustForm.adjustmentReason,
    });

    message.success('金额调整成功');
    
    // 重新加载数据
    loadPaymentData();
    if (activeTabKey.value === 'billInfo') {
      loadBillInfoData();
    }
  } catch (error) {
    message.error('金额调整失败，请稍后重试');
  }
}
```

### 5. 用户界面设计

#### 操作按钮
- 在每个账单行添加"金额调整"按钮
- 按钮样式：`type="link" size="small"`
- 点击后打开调整弹窗

#### 调整弹窗
- **弹窗标题**: "账单金额调整"
- **账单信息展示**: 账单编号、用户姓名、当前总金额、当前调整金额
- **调整金额输入**: 支持正负数，精确到小数点后2位
- **调整原因输入**: 必填项，最大200字符，支持字符计数

### 6. 表格更新

#### 窗口收费标签页
```vue
<Table
  :data-source="billList"
  :columns="billColumnsWithActions"
  :row-key="(record) => record.billId"
  :row-selection="{ onChange: handleBillSelectionChange }"
>
  <template #bodyCell="{ column, record }">
    <template v-if="column.key === 'action'">
      <Button
        type="link"
        size="small"
        @click="openBillAdjustModal(record)"
      >
        金额调整
      </Button>
    </template>
  </template>
</Table>
```

## 业务逻辑说明

### 调整规则
1. **调整金额**: 支持正数（增加金额）和负数（减少金额）
2. **调整原因**: 必填项，用于记录调整的业务原因
3. **权限控制**: 只有有权限的操作员才能进行调整
4. **数据更新**: 调整成功后自动刷新相关数据

### 数据流程
1. 用户点击"金额调整"按钮
2. 系统打开调整弹窗，显示当前账单信息
3. 用户输入调整金额和调整原因
4. 系统验证输入数据的有效性
5. 调用后端接口执行调整
6. 调整成功后刷新账单数据

### 验证机制
- **前端验证**: 调整原因必填
- **数据类型**: 调整金额自动转换为数字类型
- **精度控制**: 金额精确到小数点后2位

## 用户体验优化

### 1. 直观的操作入口
- 每个账单行都有独立的调整按钮
- 按钮文字清晰明确："金额调整"

### 2. 信息展示完整
- 弹窗中显示完整的账单信息
- 包含当前总金额和已有调整金额
- 帮助用户做出准确的调整决策

### 3. 输入引导清晰
- 调整金额输入框有明确的提示信息
- 说明正数和负数的含义
- 调整原因输入框有字符计数

### 4. 操作反馈及时
- 调整成功后显示成功消息
- 自动关闭弹窗并刷新数据
- 调整失败时显示错误信息

## 数据更新策略

### 调整成功后的数据刷新
1. **窗口收费数据**: 重新加载待缴费账单列表
2. **账单信息数据**: 如果当前在账单信息标签页，同步更新
3. **表单重置**: 清空调整表单数据
4. **状态重置**: 关闭弹窗，清空当前选中账单

### 数据一致性保证
- 调整操作是原子性的
- 成功后立即刷新相关视图
- 确保用户看到最新的数据状态

## 安全性考虑

### 1. 输入验证
- 调整原因必填验证
- 调整金额数据类型验证
- 防止空值或无效数据提交

### 2. 操作记录
- 调整原因强制记录
- 便于后续审计和追溯
- 支持业务合规要求

### 3. 权限控制
- 依赖后端接口的权限验证
- 前端仅提供操作界面
- 实际权限控制在后端实现

## 扩展性设计

### 1. 接口兼容性
- 使用现有的 `billAdjustConsumption` 接口
- 参数结构清晰，易于扩展
- 支持未来添加更多调整类型

### 2. 组件复用性
- 调整弹窗设计为独立组件
- 可在其他页面复用
- 表格操作列设计通用

### 3. 功能扩展性
- 可以轻松添加其他操作按钮
- 支持批量调整功能扩展
- 可集成更复杂的调整规则

## 测试建议

### 功能测试
- 测试正数和负数调整
- 测试必填项验证
- 测试数据刷新机制

### 用户体验测试
- 测试操作流程的流畅性
- 验证提示信息的准确性
- 确认错误处理的友好性

### 边界条件测试
- 测试极大和极小的调整金额
- 测试特殊字符的调整原因
- 测试网络异常情况的处理
