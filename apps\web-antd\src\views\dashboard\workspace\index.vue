<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { preferences } from '@vben/preferences';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';

// 导入水费系统相关API
import { billList, billPendingIssueList } from '#/api/waterfee/bill';
import { incomeSummary } from '#/api/waterfee/income';
import { meterList } from '#/api/waterfee/meter';
import { getReadingTaskList } from '#/api/waterfee/meterReading';

const userStore = useUserStore();
const router = useRouter();

// 统计数据
const statistics = reactive({
  totalIncome: 0,
  billPaymentAmount: 0,
  billPaymentCount: 0,
  pendingBillCount: 0,
  meterCount: {
    total: 0,
    mechanical: 0,
    intelligence: 0,
  },
  taskCount: {
    total: 0,
    pending: 0,
    inProgress: 0,
    completed: 0,
  },
});

// 近30天收入趋势数据
const incomeChartData = ref({
  dates: [],
  amounts: [],
  counts: [],
});

// 待办任务列表
const todoItems = ref([
  {
    id: 'default-1',
    title: '欢迎使用',
    content: '欢迎使用瓜州供排水中心管理系统工作台',
    type: 'success',
    url: '/workspace',
  },
]);

// 最近账单数据
const recentBills = ref([
  // 添加默认的测试数据，确保有内容显示
  {
    id: 'test-1',
    billNumber: 'BILL2023001',
    userName: '测试用户',
    meterNo: 'M202301',
    consumptionVolume: 10.5,
    totalAmount: 150,
    billStatus: 'PAID',
    billMonth: '2023-05',
  },
  {
    id: 'test-2',
    billNumber: 'BILL2023002',
    userName: '示例用户',
    meterNo: 'M202302',
    consumptionVolume: 8.2,
    totalAmount: 120,
    billStatus: 'PENDING',
    billMonth: '2023-05',
  },
]);

// 今日日期
const today = dayjs().format('YYYY-MM-DD');

// 添加收缴情况数据
const paymentStats = reactive({
  // 收缴率
  paymentRate: 0,
  // 已收缴笔数
  paidCount: 0,
  // 待收缴笔数
  pendingCount: 0,
  // 收缴金额
  paidAmount: 0,
  // 环比增长
  growth: 0,
});

// 初始化近30天收入数据
function initIncomeChartData() {
  const currentDate = dayjs();
  const dates = [];
  const amounts = [];
  const counts = [];

  // 生成一些模拟数据，确保有足够的随机性但也有一定规律
  for (let i = 29; i >= 0; i--) {
    const date = currentDate.subtract(i, 'day');
    dates.push(date.format('MM-DD'));

    // 生成随机数据，但带有一定的趋势，比如周末收入更高
    const weekday = date.day(); // 0是周日，6是周六
    const isWeekend = weekday === 0 || weekday === 6;
    const baseAmount = isWeekend ? 1200 : 800;
    // 添加随机波动和上升趋势
    const trendFactor = 1 + (30 - i) / 60; // 缓慢上升趋势
    const amount = Math.round((baseAmount + Math.random() * 500) * trendFactor);
    const count = Math.floor(3 + Math.random() * (isWeekend ? 12 : 8));

    amounts.push(amount);
    counts.push(count);
  }

  // 直接设置值，确保响应式更新
  incomeChartData.value.dates = dates;
  incomeChartData.value.amounts = amounts;
  incomeChartData.value.counts = counts;

  console.log('收入图表数据已初始化，数据点数量:', dates.length);
  console.log(
    '收入金额范围:',
    Math.min(...amounts),
    '至',
    Math.max(...amounts),
  );

  // 初始化后立即计算统计数据
  calculateIncomeStats();
}

// 计算30天收入统计数据
const incomeStats = reactive({
  maxDay: { date: '', amount: 0 },
  minDay: { date: '', amount: 9999 },
  avgAmount: 0,
  growth: 0, // 相比上月同期增长率
});

// 计算收入统计数据
function calculateIncomeStats() {
  const { dates, amounts } = incomeChartData.value;
  if (dates.length === 0 || amounts.length === 0) return;

  let maxAmount = 0;
  let minAmount = 9999;
  let maxIndex = 0;
  let minIndex = 0;
  let total = 0;

  // 计算最大、最小和平均值
  amounts.forEach((amount, index) => {
    total += amount;
    if (amount > maxAmount) {
      maxAmount = amount;
      maxIndex = index;
    }
    if (amount < minAmount) {
      minAmount = amount;
      minIndex = index;
    }
  });

  // 计算增长率（最后15天与前15天相比）
  const firstHalf = amounts.slice(0, 15).reduce((sum, val) => sum + val, 0);
  const secondHalf = amounts.slice(15).reduce((sum, val) => sum + val, 0);
  const growth =
    firstHalf > 0 ? ((secondHalf - firstHalf) / firstHalf) * 100 : 0;

  incomeStats.maxDay = { date: dates[maxIndex], amount: maxAmount };
  incomeStats.minDay = { date: dates[minIndex], amount: minAmount };
  incomeStats.avgAmount = Math.round(total / amounts.length);
  incomeStats.growth = Number.parseFloat(growth.toFixed(2));
}

// 待审核抄表表册
async function fetchPendingBills() {
  try {
    const res = await billPendingIssueList({
      pageNum: 1,
      pageSize: 1,
    });
    statistics.pendingBillCount = res.total || 0;
  } catch (error) {
    console.error('获取待审核抄表失败：', error);
  }
}

// 获取收入统计信息
async function fetchIncomeSummary() {
  try {
    const res = await incomeSummary({
      startTime: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
      endTime: today,
    });

    if (res) {
      statistics.totalIncome = res.totalIncomeAmount || 0;
      statistics.billPaymentAmount = res.billPaymentAmount || 0;
      statistics.billPaymentCount = res.billPaymentCount || 0;
    }
  } catch (error) {
    console.error('获取收入统计信息失败：', error);
  }
}

// 获取水表统计信息
async function fetchMeterStats() {
  try {
    const mechanical = await meterList({
      pageNum: 1,
      pageSize: 1,
      meterType: '1', // 机械表
    });

    const intelligence = await meterList({
      pageNum: 1,
      pageSize: 1,
      meterType: '2', // 物联网表
    });

    const totalRes = await meterList({
      pageNum: 1,
      pageSize: 1,
    });

    statistics.meterCount.mechanical = mechanical.total || 0;
    statistics.meterCount.intelligence = intelligence.total || 0;
    statistics.meterCount.total = totalRes.total || 0;
  } catch (error) {
    console.error('获取水表统计信息失败：', error);
  }
}

// 获取任务统计信息
async function fetchTaskStats() {
  try {
    const totalRes = await getReadingTaskList({
      pageNum: 1,
      pageSize: 1,
    });

    const pendingRes = await getReadingTaskList({
      pageNum: 1,
      pageSize: 1,
      taskStatus: 'PENDING', // 待处理状态
    });

    const inProgressRes = await getReadingTaskList({
      pageNum: 1,
      pageSize: 1,
      taskStatus: 'IN_PROGRESS', // 进行中状态
    });

    const completedRes = await getReadingTaskList({
      pageNum: 1,
      pageSize: 1,
      taskStatus: 'COMPLETED', // 已完成状态
    });

    statistics.taskCount.total = totalRes.total || 0;
    statistics.taskCount.pending = pendingRes.total || 0;
    statistics.taskCount.inProgress = inProgressRes.total || 0;
    statistics.taskCount.completed = completedRes.total || 0;
  } catch (error) {
    console.error('获取任务统计信息失败：', error);
  }
}

// 获取最近账单列表
async function fetchRecentBills() {
  try {
    console.log('开始获取最近账单列表...');
    const res = await billList({
      pageNum: 1,
      pageSize: 10, // 增加为10条记录
    });

    console.log('获取到账单列表响应:', res);

    // 数据源可能在rows或records字段中
    let billsData = [];
    if (res && res.rows && Array.isArray(res.rows)) {
      console.log('使用rows数组，长度:', res.rows.length);
      billsData = res.rows;
    } else if (res && res.records && Array.isArray(res.records)) {
      console.log('使用records数组，长度:', res.records.length);
      billsData = res.records;
    } else {
      console.warn('未找到有效的账单数据数组');
      return; // 保留默认数据
    }

    if (billsData.length === 0) {
      console.warn('账单数据数组为空');
      return; // 保留默认数据
    }

    // 映射账单数据
    const mappedBills = billsData.map((bill) => {
      return {
        id: bill.billId,
        billNumber: bill.billNumber || '未知编号',
        userName: bill.userName || '未知用户',
        meterNo: bill.meterNo || '未知表号',
        consumptionVolume: Number(bill.consumptionVolume) || 0,
        totalAmount: Number(bill.totalAmount) || 0,
        billStatus: bill.billStatus || 'PENDING',
        billMonth: bill.billMonth || '-',
      };
    });

    console.log('处理后的账单数据:', mappedBills);

    // 更新状态，最多存储10条
    if (mappedBills.length > 0) {
      recentBills.value = mappedBills.slice(0, 10);
      console.log(
        '已更新账单数据到recentBills，长度:',
        recentBills.value.length,
      );
    }
  } catch (error) {
    console.error('获取最近账单失败:', error);
  }
}

// 构建待办任务列表
async function buildTodoList() {
  try {
    console.log('开始构建待办事项列表...');
    console.log('当前统计数据:', JSON.stringify(statistics, null, 2));

    // 根据统计数据构建待办事项
    const todos = [];

    // 检查是否有待处理的抄表审核
    if (statistics.pendingBillCount && statistics.pendingBillCount > 0) {
      todos.push({
        id: 'pending-bills',
        title: '待处理抄表审核',
        content: `有 ${statistics.pendingBillCount} 个抄表待审核`,
        type: 'warning',
        url: '/businessCharges/smartReading/meterReading/audit',
      });
    }

    // 检查是否有待处理的抄表任务
    if (statistics.taskCount.pending && statistics.taskCount.pending > 0) {
      todos.push({
        id: 'pending-tasks',
        title: '待处理抄表任务',
        content: `有 ${statistics.taskCount.pending} 个抄表任务待处理`,
        type: 'info',
        url: '/meter-reading/task',
      });
    }

    // 添加默认的待办事项
    todos.push(
      {
        id: 'check-income',
        title: '检查今日收入',
        content: '查看今日收入情况并核对数据',
        type: 'success',
        url: '/businessCharges/chargeManagement/income',
      },
      {
        id: 'check-reports',
        title: '查看月度报表',
        content: '查看本月收费情况分析报表',
        type: 'primary',
        url: '/analytics',
      },
    );

    console.log('构建待办事项列表：', todos);

    // 确保待办事项列表不为空
    todoItems.value =
      todos.length > 0
        ? todos
        : [
            {
              id: 'default',
              title: '暂无待办事项',
              content: '当前没有需要处理的待办事项',
              type: 'default',
              url: '/workspace',
            },
          ];

    // 打印最终的待办事项列表
    console.log('最终待办事项列表：', todoItems.value);
  } catch (error) {
    console.error('构建待办事项列表失败：', error);
    console.error('错误详情:', error.message, error.stack);

    // 发生错误时，确保至少有一个默认项
    todoItems.value = [
      {
        id: 'error',
        title: '系统提示',
        content: '获取待办事项时发生错误，请稍后刷新页面',
        type: 'error',
        url: '/workspace',
      },
      {
        id: 'default-fallback',
        title: '默认待办项',
        content: '这是一个默认的待办事项，确保列表不为空',
        type: 'success',
        url: '/workspace',
      },
    ];
  }
}

// 快速导航项
const quickNavItems = [
  {
    icon: 'ion:document-text-outline',
    title: '营业收费系统<br/>账单管理',
    color: '#1fdaca',
    url: '/businessCharges/chargeManagement/billIssue',
  },
  {
    icon: 'ion:water-outline',
    title: '统一集抄平台<br/>抄表管理',
    color: '#3fb27f',
    url: '/unifiedCopy/smartReading/meterReading/readingTask',
  },
  {
    icon: 'ion:people-outline',
    title: 'xxx<br/>xxx',
    color: '#e18525',
    url: '/workspace',
  },
  {
    icon: 'ion:calculator-outline',
    title: 'xxx<br/>xxx',
    color: '#bf0c2c',
    url: '/workspace',
  },
  {
    icon: 'ion:cash-outline',
    title: 'xxx<br/>xxx',
    color: '#00d8ff',
    url: '/workspace',
  },
  {
    icon: 'ion:stats-chart-outline',
    title: 'xxx<br/>xxx',
    color: '#4daf1b',
    url: '/workspace',
  },
];

// 导航到指定路由
function navTo(item) {
  if (item.url) {
    router.push(item.url).catch((error) => {
      console.error('导航失败:', error);
    });
  }
}

// 获取收缴情况数据
async function fetchPaymentStats() {
  try {
    // 获取当前月份
    const currentMonth = dayjs().format('YYYY-MM');

    // 查询已支付账单
    const paidRes = await billList({
      pageNum: 1,
      pageSize: 1,
      billStatus: 'PAID',
      billMonth: currentMonth.replace('-', ''),
    });

    // 查询未支付账单
    const pendingRes = await billList({
      pageNum: 1,
      pageSize: 1,
      billStatus: 'ISSUED', // 已发行但未支付
      billMonth: currentMonth.replace('-', ''),
    });

    // 获取收入统计
    const currentMonthStart = dayjs().startOf('month').format('YYYY-MM-DD');
    const currentMonthEnd = dayjs().endOf('month').format('YYYY-MM-DD');
    const lastMonthStart = dayjs()
      .subtract(1, 'month')
      .startOf('month')
      .format('YYYY-MM-DD');
    const lastMonthEnd = dayjs()
      .subtract(1, 'month')
      .endOf('month')
      .format('YYYY-MM-DD');

    const currentMonthIncomeRes = await incomeSummary({
      startTime: currentMonthStart,
      endTime: currentMonthEnd,
    });

    const lastMonthIncomeRes = await incomeSummary({
      startTime: lastMonthStart,
      endTime: lastMonthEnd,
    });

    // 设置数据
    const paidCount = paidRes.total || 0;
    const pendingCount = pendingRes.total || 0;
    const totalCount = paidCount + pendingCount;

    paymentStats.paidCount = paidCount;
    paymentStats.pendingCount = pendingCount;
    paymentStats.paymentRate =
      totalCount > 0 ? Math.round((paidCount / totalCount) * 100) : 0;

    // 设置收缴金额
    paymentStats.paidAmount = currentMonthIncomeRes?.billPaymentAmount || 0;

    // 计算环比增长率
    const lastMonthAmount = lastMonthIncomeRes?.billPaymentAmount || 0;
    paymentStats.growth =
      lastMonthAmount > 0
        ? Number(
            (
              ((paymentStats.paidAmount - lastMonthAmount) / lastMonthAmount) *
              100
            ).toFixed(1),
          )
        : 0;

    console.log('收缴情况数据已加载:', paymentStats);
  } catch (error) {
    console.error('获取收缴情况数据失败:', error);
  }
}

// 获取所有工作台数据
async function fetchAllData() {
  try {
    console.log('开始获取所有工作台数据...');

    // 先获取各种数据
    await Promise.all([
      fetchPendingBills(),
      fetchIncomeSummary(),
      fetchMeterStats(),
      fetchTaskStats(),
      fetchPaymentStats(), // 添加收缴情况数据获取
    ]);

    console.log('所有API数据获取完成，开始构建待办事项');

    // 然后构建待办事项列表
    await buildTodoList();

    console.log('工作台数据初始化完成');
  } catch (error) {
    console.error('获取工作台数据失败：', error);
    throw error; // 使用throw而不是Promise.reject
  }
}

onMounted(() => {
  console.log('工作台组件已挂载');
  console.log('当前待办事项列表初始状态:', todoItems.value);

  // 立即添加一个默认待办事项，确保有内容显示
  todoItems.value = [
    {
      id: 'welcome',
      title: '欢迎使用瓜州供排水中心管理系统工作台',
      content: '您可以在这里看到所有需要处理的待办事项',
      type: 'success',
      url: '/workspace',
    },
    {
      id: 'loading',
      title: '数据加载中',
      content: '系统正在加载您的待办事项数据，请稍候...',
      type: 'processing',
      url: '/workspace',
    },
  ];

  console.log('设置默认待办事项后:', todoItems.value);

  // 初始化收入图表数据
  initIncomeChartData();

  // 使用API获取真实账单数据
  fetchRecentBills()
    .then(() => {
      console.log('账单数据加载完成');
    })
    .catch((error) => {
      console.error('账单数据加载失败', error);
    });

  // 获取其他数据
  fetchAllData()
    .then(() => {
      console.log('所有数据加载完成，待办事项数量：', todoItems.value.length);
      console.log(
        '最终待办事项列表状态:',
        JSON.stringify(todoItems.value, null, 2),
      );
    })
    .catch((error) => {
      console.error('数据加载失败：', error);
      // 确保即使发生错误，页面也能显示一些内容
      if (todoItems.value.length <= 2) {
        todoItems.value.push({
          id: 'error-fallback',
          title: '数据加载失败',
          content: '获取数据时发生错误，请稍后刷新页面重试',
          type: 'error',
          url: '/workspace',
        });
      }
    });
});

// 账单状态映射
const billStatusMap = {
  PENDING: '待处理',
  ISSUED: '已发行',
  PAID: '已支付',
  OVERDUE: '已逾期',
  PARTIAL_PAID: '部分支付',
  DRAFT: '草稿',
};

// 获取账单状态样式
function getBillStatusType(status) {
  const map = {
    PENDING: 'default',
    ISSUED: 'warning',
    PAID: 'success',
    OVERDUE: 'error',
    PARTIAL_PAID: 'processing',
    DRAFT: 'default',
  };
  return map[status] || 'default';
}

// 格式化金额
function formatAmount(amount) {
  if (amount === null || amount === undefined) {
    return '¥0.00';
  }

  // 确保amount是数字
  const numAmount = Number(amount);
  if (Number.isNaN(numAmount)) {
    return '¥0.00';
  }

  return `¥${numAmount.toFixed(2)}`;
}
</script>

<template>
  <div class="dashboard-workspace bg-gray-50 p-6">
    <!-- 顶部欢迎区域 -->
    <div
      class="welcome-section mb-6 overflow-hidden rounded-lg bg-white shadow-sm"
    >
      <div class="flex items-center p-6">
        <div class="avatar-container mr-6">
          <a-avatar
            :size="80"
            :src="userStore.userInfo?.avatar || preferences.app.defaultAvatar"
            class="border-4 border-blue-100"
          />
        </div>
        <div class="user-info flex-1">
          <h1 class="mb-1 text-xl font-bold text-gray-800">
            您好，{{
              userStore.userInfo?.realName
            }}，欢迎使用瓜州供排水中心管理系统工作台!
          </h1>
          <p class="text-gray-500">今日是 {{ today }}，祝您工作顺利!</p>
        </div>
        <div class="stats-container flex items-center gap-8">
          <div class="stat-item text-center">
            <p class="text-sm text-gray-500">待办</p>
            <p class="text-2xl font-bold text-blue-600">
              {{ todoItems.length || 0 }}
            </p>
          </div>
          <div class="stat-item text-center">
            <p class="text-sm text-gray-500">项目</p>
            <p class="text-2xl font-bold text-green-600">8</p>
          </div>
          <div class="stat-item text-center">
            <p class="text-sm text-gray-500">团队</p>
            <p class="text-2xl font-bold text-amber-600">300</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片部分 -->
    <div
      class="stats-cards mb-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4"
    >
      <!-- 收入统计卡片 -->
      <div
        class="stat-card h-full overflow-hidden rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 shadow-sm"
      >
        <div class="flex h-full flex-col justify-between p-6">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="font-medium text-blue-800">30天收入总额</h3>
            <div class="icon-container rounded-lg bg-blue-500 p-2 text-white">
              <icon-tabler:cash class="text-xl" />
            </div>
          </div>
          <div class="amount mb-2 text-3xl font-bold text-blue-800">
            {{ formatAmount(statistics.totalIncome) }}
          </div>
          <div class="text-xs text-blue-700">
            账单缴费: {{ formatAmount(statistics.billPaymentAmount) }} /
            <span class="font-medium">{{ statistics.billPaymentCount }}</span>
            笔
          </div>
        </div>
      </div>

      <!-- 待审核抄表表册卡片 -->
      <div
        class="stat-card h-full overflow-hidden rounded-lg bg-gradient-to-br from-green-50 to-green-100 shadow-sm"
      >
        <div class="flex h-full flex-col justify-between p-6">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="font-medium text-green-800">抄表待审核数量</h3>
            <div class="icon-container rounded-lg bg-green-500 p-2 text-white">
              <icon-tabler:file-invoice class="text-xl" />
            </div>
          </div>
          <div class="amount mb-2 text-3xl font-bold text-green-800">
            {{ statistics.pendingBillCount }}
          </div>
          <div class="text-xs text-green-700">
            <a-button
              type="link"
              size="small"
              class="p-0 text-green-700 hover:text-green-900"
              @click="
                navTo({
                  url: '/businessCharges/smartReading/meterReading/audit',
                })
              "
            >
              查看待审核抄表表册 →
            </a-button>
          </div>
        </div>
      </div>

      <!-- 水表统计卡片 -->
      <div
        class="stat-card h-full overflow-hidden rounded-lg bg-gradient-to-br from-amber-50 to-amber-100 shadow-sm"
      >
        <div class="flex h-full flex-col justify-between p-6">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="font-medium text-amber-800">水表总数</h3>
            <div class="icon-container rounded-lg bg-amber-500 p-2 text-white">
              <icon-tabler:gauge class="text-xl" />
            </div>
          </div>
          <div class="amount mb-2 text-3xl font-bold text-amber-800">
            {{ statistics.meterCount.total }}
          </div>
          <div class="text-xs text-amber-700">
            机械表:
            <span class="font-medium">{{
              statistics.meterCount.mechanical
            }}</span>
            / 物联网表:
            <span class="font-medium">{{
              statistics.meterCount.intelligence
            }}</span>
          </div>
        </div>
      </div>

      <!-- 抄表任务卡片 -->
      <div
        class="stat-card h-full overflow-hidden rounded-lg bg-gradient-to-br from-purple-50 to-purple-100 shadow-sm"
      >
        <div class="flex h-full flex-col justify-between p-6">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="font-medium text-purple-800">抄表任务</h3>
            <div class="icon-container rounded-lg bg-purple-500 p-2 text-white">
              <icon-tabler:list-check class="text-xl" />
            </div>
          </div>
          <div class="amount mb-2 text-3xl font-bold text-purple-800">
            {{ statistics.taskCount.total }}
          </div>
          <div class="text-xs text-purple-700">
            待处理: {{ statistics.taskCount.pending }} / 进行中:
            {{ statistics.taskCount.inProgress }} / 已完成:
            {{ statistics.taskCount.completed }}
          </div>
        </div>
      </div>
    </div>

    <div class="content-section flex flex-col gap-6 lg:flex-row">
      <div class="left-content w-full lg:w-3/5">
        <!-- 最近账单 -->
        <div
          class="recent-bills mb-6 overflow-hidden rounded-lg bg-white shadow-sm"
        >
          <div
            class="flex items-center justify-between border-b border-gray-100 p-4"
          >
            <h2 class="text-lg font-medium text-gray-800">最近账单</h2>
            <a-button
              type="link"
              class="text-blue-600"
              @click="navTo({ url: '/businessCharges/chargeManagement/bills' })"
            >
              查看更多
            </a-button>
          </div>
          <div class="p-4">
            <!-- 账单卡片展示 - 使用固定高度和滚动条 -->
            <div class="h-[320px] overflow-y-auto pr-1">
              <div
                v-for="bill in recentBills"
                :key="bill.id"
                class="todo-item mb-3 cursor-pointer rounded-md border border-gray-200 p-3 transition-all hover:shadow-sm"
                @click="
                  navTo({
                    url: `/businessCharges/chargeManagement/detail?id=${bill.id}`,
                  })
                "
              >
                <div class="mb-1.5 flex items-center justify-between">
                  <div class="flex items-center">
                    <a-badge :status="getBillStatusType(bill.billStatus)" />
                    <span class="ml-2 font-medium text-gray-800">{{
                      bill.billNumber
                    }}</span>
                  </div>
                  <a-tag :color="getBillStatusType(bill.billStatus)">
                    {{ billStatusMap[bill.billStatus] || bill.billStatus }}
                  </a-tag>
                </div>

                <div class="mb-1.5 grid grid-cols-2 gap-x-2 gap-y-1">
                  <div class="flex items-center text-sm text-gray-600">
                    <span class="mr-1 text-gray-500">用户:</span>
                    <span class="font-medium">{{ bill.userName }}</span>
                  </div>
                  <div class="flex items-center text-sm text-gray-600">
                    <span class="mr-1 text-gray-500">水表号:</span>
                    <span class="font-medium">{{ bill.meterNo }}</span>
                  </div>
                  <div class="flex items-center text-sm text-gray-600">
                    <span class="mr-1 text-gray-500">用水量:</span>
                    <span class="font-medium"
                      >{{ bill.consumptionVolume }} m³</span
                    >
                  </div>
                  <div class="flex items-center text-sm text-gray-600">
                    <span class="mr-1 text-gray-500">账单月份:</span>
                    <span class="font-medium">{{
                      bill.billMonth
                        ? `${bill.billMonth.substring(0, 4)}-${bill.billMonth.substring(4)}`
                        : '-'
                    }}</span>
                  </div>
                </div>

                <div
                  class="flex items-center justify-between border-t border-gray-100 pt-1.5"
                >
                  <div class="flex items-baseline">
                    <span class="mr-1 text-xs text-gray-500">金额:</span>
                    <span class="text-base font-medium text-blue-600">{{
                      formatAmount(bill.totalAmount)
                    }}</span>
                  </div>
                  <a-button
                    type="link"
                    size="small"
                    class="p-0 text-blue-600"
                    @click.stop="
                      navTo({
                        url: `/businessCharges/chargeManagement/detail?id=${bill.id}`,
                      })
                    "
                  >
                    查看详情
                  </a-button>
                </div>
              </div>
              <!-- 空状态 -->
              <div
                v-if="recentBills.length === 0"
                class="flex h-32 items-center justify-center rounded-lg border border-gray-100"
              >
                <a-empty description="暂无账单数据" />
              </div>
            </div>
          </div>
        </div>

        <!-- 收入趋势图表 -->
        <div class="income-trend overflow-hidden rounded-lg bg-white shadow-sm">
          <div class="border-b border-gray-100 p-4">
            <h2 class="text-lg font-medium text-gray-800">近30天收入趋势</h2>
          </div>
          <div class="p-4">
            <!-- 收入统计数据概览 -->
            <div
              class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"
            >
              <div class="rounded-lg bg-blue-50 p-3 shadow-sm">
                <div class="text-sm text-gray-500">总收入</div>
                <div class="text-xl font-bold text-blue-600">
                  {{ formatAmount(statistics.totalIncome) }}
                </div>
              </div>
              <div class="rounded-lg bg-green-50 p-3 shadow-sm">
                <div class="text-sm text-gray-500">日均收入</div>
                <div class="text-xl font-bold text-green-600">
                  {{ formatAmount(incomeStats.avgAmount) }}
                </div>
              </div>
              <div class="rounded-lg bg-amber-50 p-3 shadow-sm">
                <div class="text-sm text-gray-500">最高日收入</div>
                <div class="text-xl font-bold text-amber-600">
                  {{ formatAmount(incomeStats.maxDay.amount) }}
                </div>
                <div class="text-xs text-gray-400">
                  {{ incomeStats.maxDay.date }}
                </div>
              </div>
              <div class="rounded-lg bg-indigo-50 p-3 shadow-sm">
                <div class="text-sm text-gray-500">环比增长</div>
                <div class="flex items-center">
                  <span
                    class="text-xl font-bold"
                    :class="
                      incomeStats.growth >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    "
                  >
                    {{ incomeStats.growth >= 0 ? '+' : ''
                    }}{{ incomeStats.growth }}%
                  </span>
                  <span class="ml-1 text-lg">
                    <icon-tabler:trending-up
                      v-if="incomeStats.growth >= 0"
                      class="text-green-500"
                    />
                    <icon-tabler:trending-down v-else class="text-red-500" />
                  </span>
                </div>
              </div>
            </div>

            <!-- 收入柱状图 -->
            <div
              class="h-48 w-full overflow-hidden rounded-lg border border-gray-100 p-2"
            >
              <div
                v-if="incomeChartData.amounts.length > 0"
                class="relative h-full"
              >
                <!-- 数据标签-最大值 -->
                <div
                  class="absolute left-0 right-0 top-0 pr-2 text-right text-xs text-gray-400"
                >
                  {{ formatAmount(Math.max(...incomeChartData.amounts)) }}
                </div>

                <!-- 柱状图 -->
                <div class="flex h-full items-end pb-5 pt-5">
                  <div
                    v-for="(amount, index) in incomeChartData.amounts.slice(
                      -14,
                    )"
                    :key="index"
                    class="group relative mx-px flex flex-1 flex-col items-center"
                  >
                    <!-- 柱子 -->
                    <div
                      class="w-full rounded-t bg-blue-500 transition-all duration-300 hover:bg-blue-600"
                      :style="{
                        height: `${(amount / Math.max(...incomeChartData.amounts)) * 80}%`,
                        opacity: 0.7 + (index + 1) / 40,
                      }"
                    ></div>

                    <!-- 悬浮提示 -->
                    <div
                      class="absolute bottom-full mb-1 hidden group-hover:block"
                    >
                      <div
                        class="whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-white"
                      >
                        {{ formatAmount(amount) }}
                      </div>
                    </div>

                    <!-- 日期标签 -->
                    <div class="mt-1 text-xs text-gray-500">
                      {{ incomeChartData.dates.slice(-14)[index] }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无数据状态 -->
              <div v-else class="flex h-full items-center justify-center">
                <a-empty description="暂无收入数据" />
              </div>
            </div>

            <div class="mt-3 text-center">
              <a-button
                type="link"
                class="text-blue-600"
                @click="
                  navTo({ url: '/businessCharges/chargeManagement/income' })
                "
              >
                查看详细报表
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <div class="right-content w-full lg:w-2/5">
        <!-- 快速导航 -->
        <div
          class="quick-nav mb-6 overflow-hidden rounded-lg bg-white shadow-sm"
        >
          <div class="border-b border-gray-100 p-4">
            <h2 class="text-lg font-medium text-gray-800">快速导航</h2>
          </div>
          <div class="p-4">
            <div class="grid grid-cols-3 gap-4">
              <div
                v-for="(item, index) in quickNavItems"
                :key="index"
                class="nav-item flex h-28 cursor-pointer flex-col items-center justify-center rounded-lg border border-gray-100 p-4 transition-all duration-300 hover:bg-gray-50 hover:shadow"
                @click="navTo(item)"
              >
                <div
                  class="icon-container mb-3 rounded-full p-3"
                  :style="{
                    backgroundColor: `${item.color}20`,
                    color: item.color,
                  }"
                >
                  <component :is="item.icon" class="text-xl" />
                </div>
                <div
                  class="text-center text-sm font-medium leading-snug text-gray-700"
                  v-html="item.title"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 待办事项 -->
        <div
          class="todo-list max-h-96 overflow-y-auto rounded-lg bg-white shadow-sm"
        >
          <div
            class="flex items-center justify-between border-b border-gray-100 p-4"
          >
            <h2 class="text-lg font-medium text-gray-800">待办事项</h2>
            <span class="text-xs text-gray-500">
              共 {{ todoItems.length }} 项
            </span>
          </div>
          <div class="p-4">
            <div v-if="todoItems && todoItems.length > 0">
              <div
                v-for="(item, index) in todoItems"
                :key="item.id || index"
                class="todo-item mb-4 cursor-pointer rounded-md border border-gray-200 p-3 transition-all last:mb-0 last:border-b-0 last:pb-0 hover:shadow-sm"
              >
                <div class="mb-2 flex items-center">
                  <a-badge :status="item.type || 'default'" />
                  <span class="ml-2 font-medium text-gray-800">{{
                    item.title || '未知标题'
                  }}</span>
                </div>
                <p class="mb-2 text-sm text-gray-600">
                  {{ item.content || '无内容' }}
                </p>
                <div class="flex justify-end">
                  <a-button
                    type="link"
                    size="small"
                    class="p-0 text-blue-600"
                    @click="navTo(item)"
                  >
                    查看详情
                  </a-button>
                </div>
              </div>
            </div>
            <div v-else class="flex items-center justify-center py-10">
              <a-empty description="暂无待办事项" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
