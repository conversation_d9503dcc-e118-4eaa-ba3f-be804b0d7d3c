import type { BaseEntity } from '#/api/common';

/**
 * 收入记录模型
 */
export interface IncomeModel extends BaseEntity {
  /** 收入ID */
  incomeId?: number;
  /** 收入类型（bill_payment：账单缴费，prestore：预存充值，refund：账单退费） */
  incomeType?: string;
  /** 用户编号 */
  userNo?: string;
  /** 收费员姓名 */
  collectorName?: string;
  /** 收入时间 */
  incomeTime?: string;
  /** 金额 */
  amount?: number;
  /** 支付方式（wechat：微信，alipay：支付宝，cash：现金，other：其他） */
  payMethod?: string;
  /** 备注 */
  remark?: string;

  // 额外的显示字段
  /** 用户名称（关联查询） */
  userName?: string;
  /** 用户地址（关联查询） */
  address?: string;
}

/**
 * 收入记录查询参数
 */
export interface IncomeParams {
  /** 收入ID */
  incomeId?: number;
  /** a记录类型 */
  incomeType?: string;
  /** 用户编号 */
  userNo?: string;
  /** 收入时间范围（开始） */
  startTime?: string;
  /** 收入时间范围（结束） */
  endTime?: string;
  /** 支付方式 */
  payMethod?: string;
  /** 分页参数 */
  pageNum?: number;
  /** 分页参数 */
  pageSize?: number;
}

/**
 * 收入记录统计信息
 */
export interface WaterfeeIncomeSummaryVo {
  /**
   * 账单缴费总金额
   */
  billPaymentAmount: number;

  /**
   * 账单缴费笔数
   */
  billPaymentCount: number;

  /**
   * 预存充值总金额
   */
  prestoreAmount: number;

  /**
   * 预存充值笔数
   */
  prestoreCount: number;

  /**
   * 账单退费总金额 (通常为负值，表示支出)
   */
  refundAmount: number;

  /**
   * 账单退费笔数
   */
  refundCount: number;

  /**
   * 总收入金额 (billPaymentAmount + prestoreAmount - refundAmount)
   */
  totalIncomeAmount: number;

  /**
   * 总交易笔数
   */
  totalTransactionCount: number;
}
