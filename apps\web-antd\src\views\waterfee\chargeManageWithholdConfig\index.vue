<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ChargeManageWithholdConfigVO } from '#/api/waterfee/chargeManageWithholdConfig/model.d';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space } from 'ant-design-vue';

import { GhostButton } from '#/components/global/button';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { listChargeManageWithholdConfig, delChargeManageWithholdConfig, ChargeManageWithholdConfigExport } from '#/api/waterfee/chargeManageWithholdConfig';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './data';
import chargeManageWithholdConfigDrawer from './chargeManageWithholdConfig-drawer.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await listChargeManageWithholdConfig({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  id: 'waterfee-chargeManageWithholdConfig-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [ChargeManageWithholdConfigDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: chargeManageWithholdConfigDrawer,
});

function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

async function handleEdit(record: ChargeManageWithholdConfigVO) {
  drawerApi.setData({ id: record.id });
  drawerApi.open();
}

async function handleDelete(row: ChargeManageWithholdConfigVO) {
  await delChargeManageWithholdConfig([row.id]);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: ChargeManageWithholdConfigVO) => row.id);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await delChargeManageWithholdConfig(ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(ChargeManageWithholdConfigExport, '代扣配置信息数据', tableApi.formApi.form.values);
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="代扣配置信息列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['waterfee:chargeManageWithholdConfig:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['waterfee:chargeManageWithholdConfig:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['waterfee:chargeManageWithholdConfig:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <GhostButton
            v-access:code="['waterfee:chargeManageWithholdConfig:edit']"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </GhostButton>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <GhostButton
              danger
              v-access:code="['waterfee:chargeManageWithholdConfig:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </GhostButton>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <ChargeManageWithholdConfigDrawer @reload="tableApi.query()" />
  </Page>
</template>
