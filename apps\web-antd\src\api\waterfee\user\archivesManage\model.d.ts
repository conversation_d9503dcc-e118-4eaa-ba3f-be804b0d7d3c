export interface UserVO {
  /**
   * 用户ID
   */
  userId: string;

  /**
   * 用户编号
   */
  userNo: string;

  /**
   * 客户性质（字典waterfee_user_customer_nature）
   */
  customerNature: string;

  /**
   * 用水性质（字典waterfee_user_use_water_nature）
   */
  useWaterNature: string;

  /**
   * 用水户名称
   */
  userName: string;

  /**
   * 用户状态（字典waterfee_user_user_status）
   */
  userStatus: string;

  /**
   * 审核状态（字典audit_status）
   */
  auditStatus: string;
  address: string;
}

export interface Waterfee {
  meterNo: string;
  caliber: string;
  meterType: string;
  installAddress: string;
}

export interface Price {
  priceUseWaterNature: string;
  billingMethod: string;
  ifPenalty?: string;
  penaltyType?: string;
  ifExtraCharge?: string;
  extraChargeType?: string;
}

export interface UserForm extends BaseEntity {
  /**
   * 用户ID
   */
  userId?: number | string;

  /**
   * 用户编号
   */
  userNo?: string;

  /**
   * 营业区域id
   */
  areaId?: number | string;

  /**
   * 小区id
   */
  communityId?: number | string;

  /**
   * 单元房号
   */
  unitRoomNumber?: string;

  /**
   * 客户性质（字典waterfee_user_customer_nature）
   */
  customerNature?: string;

  /**
   * 用水性质（字典waterfee_user_use_water_nature）
   */
  useWaterNature?: string;

  /**
   * 用水人数
   */
  useWaterNumber?: number;

  /**
   * 用水户名称
   */
  userName?: string;

  /**
   * 手机号码
   */
  phoneNumber?: string;

  /**
   * 证件类型
   */
  certificateType?: string;

  /**
   * 证件号码
   */
  certificateNumber?: string;

  /**
   * 用户状态（字典waterfee_user_user_status）
   */
  userStatus?: string;

  /**
   * 用水地址
   */
  address?: string;

  /**
   * 电子邮箱
   */
  email?: string;

  /**
   * 纳税人识别号
   */
  taxpayerIdentificationNumber?: number | string;

  /**
   * 供水日期
   */
  supplyDate?: string;

  /**
   * 开票名称
   */
  invoiceName?: string;

  /**
   * 发票类型（字典waterfee_user_invoice_type）
   */
  invoiceType?: string;

  /**
   * 水表编号
   */
  meterNo?: string;

  /**
   * 价格-用水性质（字典waterfee_user_use_water_nature）
   */
  priceUseWaterNature?: string;

  /**
   * 计费方式（字典waterfee_user_billing_method）
   */
  billingMethod?: string;

  /**
   * 是否有违约金（字典yes_no）
   */
  ifPenalty?: string;

  /**
   * 违约金类型（字典waterfee_user_penalty_type）
   */
  penaltyType?: string;

  /**
   * 是否有附加费（字典yes_no）
   */
  ifExtraCharge?: string;

  /**
   * 附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
   */
  extraChargeType?: string;

  /**
   * 审核状态（字典audit_status）
   */
  auditStatus: string;
}

export interface UserQuery extends PageQuery {
  /**
   * 用户ID
   */
  userId?: number | string;

  /**
   * 用户编号
   */
  userNo?: string;

  /**
   * 客户性质（字典waterfee_user_customer_nature）
   */
  customerNature?: string;

  /**
   * 用水性质（字典waterfee_user_use_water_nature）
   */
  useWaterNature?: string;

  /**
   * 用水户名称
   */
  userName?: string;

  /**
   * 用户状态（字典waterfee_user_user_status）
   */
  userStatus?: string;

  /**
   * 审核状态（字典audit_status）
   */
  auditStatus: string;

  /**
   * 日期范围参数
   */
  params?: any;

  /**
   * 搜索值 用户名称或编号
   */
  searchValue?: string;
}

export interface UserFlowStepsInfo {
  userBasicInfo: UserVO;
  userPrice?: Price;
  waterfeeMeterBo?: Waterfee;
}

/**
 * 批量报停用水用户请求参数
 */
export interface WaterfeeUserBatchDeactivateBo {
  /** 用户ID列表 */
  userIds: Array<number | string>;
  /** 报停原因 */
  deactivateReason: string;
}

/**
 * 批量销户用水用户请求参数
 */
export interface WaterfeeUserBatchCancellationBo {
  /** 用户ID列表 */
  userIds: Array<number | string>;
  /** 销户原因 */
  cancellationReason: string;
}

export interface UserImportParam {
  updateSupport: boolean;
  file: Blob | File;
}
