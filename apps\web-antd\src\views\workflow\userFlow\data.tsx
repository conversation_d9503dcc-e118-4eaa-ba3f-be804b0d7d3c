import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';

import { getPopupContainer } from '@vben/utils';

import { communityOptions } from '#/api/waterfee/community';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'searchValue',
    label: '关键字',
    componentProps: {
      placeholder: '请输入户号或名称',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'useWaterNature',
    label: '用水性质',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_user_customer_nature'),
    },
    fieldName: 'customerNature',
    label: '客户性质',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('wf_business_status'),
    },
    fieldName: 'auditStatus',
    label: '审核状态',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  // {
  //   title: '用户ID',
  //   field: 'userId',
  // },
  {
    title: '户号',
    field: 'userNo',
  },
  {
    title: '用户名称',
    field: 'userName',
  },
  {
    title: '用水性质',
    field: 'useWaterNature',
    slots: {
      default: ({ row }) => {
        return renderDict(row.useWaterNature, 'waterfee_user_use_water_nature');
      },
    },
  },
  {
    title: '客户性质',
    field: 'customerNature',
    slots: {
      default: ({ row }) => {
        return renderDict(row.customerNature, 'waterfee_user_customer_nature');
      },
    },
  },
  {
    title: '抄表手册',
    field: 'bookName',
  },
  {
    title: '水表编号',
    field: 'meterNo',
  },
  {
    title: '用户状态',
    field: 'userStatus',
    slots: {
      default: ({ row }) => {
        return renderDict(row.userStatus, 'waterfee_user_user_status');
      },
    },
  },
  {
    title: '审核状态',
    field: 'auditStatus',
    slots: {
      default: ({ row }) => {
        return renderDict(row.auditStatus, 'wf_business_status');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 240,
  },
];

/**
 * 用户表单数据结构
 * 根据图片布局调整表单项的排列和样式
 * 只保留图片中显示的字段
 */
export const flowSchema: FormSchemaGetter = () => [
  // 隐藏字段
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'userId',
    label: '主键',
  },
  // 第一行
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '户号',
    componentProps: {
      placeholder: '输入内容',
      // disabled: true,
    },
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_customer_nature'),
    },
    fieldName: 'customerNature',
    label: '客户性质',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'useWaterNature',
    label: '用水性质',
    rules: 'required',
  },
  // 第二行
  {
    component: 'Input',
    fieldName: 'useWaterNumber',
    label: '用水人数',
    componentProps: {
      placeholder: '输入内容',
    },
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'userName',
    label: '用户姓名',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'phoneNumber',
    label: '手机号码',
    componentProps: {
      placeholder: '输入内容',
    },
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'certificateNumber',
    label: '证件号码',
    componentProps: {
      placeholder: '输入内容',
    },
    rules: 'required',
  },
  // 第三行
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_certificate_type'),
    },
    fieldName: 'certificateType',
    label: '证件类型',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_user_status'),
    },
    fieldName: 'userStatus',
    label: '用户状态',
    rules: 'required',
  },
  {
    // component: 'ApiSelect',
    // componentProps: () => {
    //   return {
    //     placeholder: '请选择',
    //     api: areaOptions,
    //     fieldNames: { label: 'areaName', value: 'areaId' },
    //   };
    // },
    component: 'TreeSelect',
    componentProps: {
      fieldNames: { label: 'areaName', value: 'areaId' },
      treeData: [],
      placeholder: '请选择所属区域',
    },
    fieldName: 'areaId',
    label: '营业区域',
    rules: 'required',
  },
  {
    component: 'ApiSelect',
    componentProps: () => {
      return {
        placeholder: '请选择',
        api: communityOptions,
        fieldNames: { label: 'communityName', value: 'id' },
      };
    },
    fieldName: 'communityId',
    label: '小区名称',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'unitRoomNumber',
    label: '单元房号',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'address',
    label: '用水地址',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'email',
    label: '电子邮箱',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'taxpayerIdentificationNumber',
    label: '纳税人识别号',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      format: 'YYYY-MM-DD HH:mm:ss',
      showTime: false,
    },
    fieldName: 'supplyDate',
    label: '供水日期',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      triggerFields: [''],
    },
    fieldName: 'invoiceName',
    label: '开票名称',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: getDictOptions('waterfee_user_invoice_type'),
    },
    fieldName: 'invoiceType',
    label: '发票类型',
    rules: 'required',
  },
];

// 水表描述项
export const meterDescSchema: DescItem[] = [
  {
    field: 'meterNo',
    label: '水表编号',
    span: 1,
  },
  {
    field: 'caliber',
    label: '水表口径',
    span: 1,
    // 如果没有值,则不转换字典
    render: (val) => val && renderDict(val, 'dnmm'),
  },
  {
    field: 'meterType',
    label: '水表类型',
    span: 1,
    render: (val) => val && renderDict(val, 'waterfee_meter_type'),
  },
  {
    field: 'installAddress',
    label: '水表地址',
    span: 1,
  },
];

// 价格描述项
export const priceDescSchema: DescItem[] = [
  // {
  //   field: 'priceUseWaterNature',
  //   label: '用水性质',
  //   span: 1,
  //   render: (val) => val && renderDict(val, 'waterfee_user_use_water_nature'),
  // },
  // {
  //   field: 'billingMethod',
  //   label: '计费方式',
  //   span: 1,
  //   render: (val) => val && renderDict(val, 'waterfee_user_billing_method'),
  // },
  // {
  //   field: 'ifPenalty',
  //   label: '是否有违约金',
  //   span: 1,
  //   render: (val) => val && renderDict(val, 'yes_no'),
  // },
  // {
  //   field: 'penaltyType',
  //   label: '违约金类型',
  //   span: 1,
  //   render: (val) => val && renderDict(val, 'waterfee_user_penalty_type'),
  //   show: (data) => data.ifPenalty === '1',
  // },
  // {
  //   field: 'ifExtraCharge',
  //   label: '是否有附加费',
  //   span: 1,
  //   render: (val) => val && renderDict(val, 'yes_no'),
  // },
  // {
  //   field: 'extraChargeType',
  //   label: '附加费类型',
  //   span: 1,
  //   render: (val) => val && renderDicts(val, 'waterfee_user_extra_charge_type'),
  //   show: (data) => data.ifExtraCharge === '1',
  // },
  {
    field: 'billingMethod',
    label: '计费方式',
    span: 1,
    render: (val) => {
      if (!val) return '-';
      return '加载中...';
    },
  },
];
