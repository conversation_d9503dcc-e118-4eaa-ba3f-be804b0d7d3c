import type { MenuRecordRaw } from '@vben-core/typings';

/**
 * 查找菜单中第一个可访问的叶子节点（没有子菜单的菜单项）
 * @param menu 菜单项
 * @returns 第一个可访问的叶子节点路径
 */
export function findFirstAccessibleLeaf(menu: MenuRecordRaw): null | string {
  // 如果当前菜单没有子菜单，则返回当前菜单路径
  if (!menu.children || menu.children.length === 0) {
    return menu.path;
  }

  // 递归查找第一个可访问的子菜单
  for (const child of menu.children) {
    if (child.show !== false) {
      // 确保菜单是可见的
      const leafPath = findFirstAccessibleLeaf(child);
      if (leafPath) {
        return leafPath;
      }
    }
  }

  // 如果没有找到可访问的子菜单，返回当前菜单路径
  return menu.path;
}

/**
 * 查找根菜单的第一个可访问的子菜单路径
 * @param rootMenu 根菜单
 * @returns 第一个可访问的子菜单路径
 */
export function findFirstChildMenuPath(rootMenu: MenuRecordRaw): string {
  if (!rootMenu.children || rootMenu.children.length === 0) {
    return rootMenu.path;
  }

  // 查找第一个可访问的子菜单
  const firstAccessibleChild = rootMenu.children.find(
    (child) => child.show !== false,
  );
  if (firstAccessibleChild) {
    return (
      findFirstAccessibleLeaf(firstAccessibleChild) || firstAccessibleChild.path
    );
  }

  return rootMenu.path;
}
