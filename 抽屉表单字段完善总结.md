# 抽屉表单字段完善总结

## 概述
针对营业外收入记录、用户关系维护、第三方对账记录、代扣记录、代扣配置信息、违约金调整、应收账分账等页面的抽屉表单字段不全问题，进行了全面的完善和优化。

## 完善的页面

### 1. 营业外收入记录 (chargeManageNonOperatingIncome)
**文件**: `apps/web-antd/src/views/waterfee/chargeManageNonOperatingIncome/data.tsx`

#### 查询表单新增字段
- 收入类型选择器 (incomeType)
- 金额数字输入框 (amount)
- 收入时间日期选择器 (incomeTime)

#### 抽屉表单新增字段
- 用户ID (userId) - 必填
- 用户姓名 (userName)
- 收入类型 (incomeType) - 必填，字典选择
- 金额 (amount) - 必填，数字输入，带单位
- 收入时间 (incomeTime) - 必填，日期时间选择
- 收款方式 (paymentMethod) - 字典选择
- 收入来源 (incomeSource)
- 操作员 (operator)
- 备注 (remark) - 文本域，500字符限制

### 2. 代扣记录 (chargeManageWithholdRecord)
**文件**: `apps/web-antd/src/views/waterfee/chargeManageWithholdRecord/data.tsx`

#### 查询表单新增字段
- 用户编号 (userNo)
- 扣款状态选择器 (status)
- 扣款金额数字输入 (amount)
- 扣款时间日期选择器 (withholdTime)

#### 抽屉表单新增字段
- 用户ID (userId) - 必填
- 用户编号 (userNo)
- 用户姓名 (userName)
- 扣款金额 (amount) - 必填，数字输入，带单位
- 银行账号 (bankAccount)
- 开户行 (bankName)
- 扣款时间 (withholdTime) - 必填，日期时间选择
- 扣款状态 (status) - 必填，字典选择
- 交易流水号 (transactionId)
- 操作员 (operator)
- 备注 (remark) - 文本域

### 3. 代扣配置信息 (chargeManageWithholdConfig)
**文件**: `apps/web-antd/src/views/waterfee/chargeManageWithholdConfig/data.tsx`

#### 查询表单新增字段
- 用户编号 (userNo)
- 银行账号 (bankAccount)
- 开户行 (bankName)
- 是否签约选择器 (signed)

#### 抽屉表单新增字段
- 用户ID (userId) - 必填
- 用户编号 (userNo)
- 用户姓名 (userName)
- 银行账号 (bankAccount) - 必填
- 开户行 (bankName) - 必填
- 账户名 (accountName)
- 是否签约 (signed) - 必填，字典选择
- 签约时间 (signTime) - 条件显示
- 取消时间 (cancelTime) - 条件显示
- 联系电话 (contactPhone)
- 操作员 (operator)
- 备注 (remark) - 文本域

### 4. 违约金调整 (chargeManagePenaltyAdjustment)
**文件**: `apps/web-antd/src/views/waterfee/chargeManagePenaltyAdjustment/data.tsx`

#### 查询表单新增字段
- 用户编号 (userNo)
- 账单ID (billId)
- 原违约金数字输入 (originalPenalty)
- 调整后违约金数字输入 (adjustedPenalty)
- 调整原因 (reason)

#### 抽屉表单新增字段
- 用户ID (userId) - 必填
- 用户编号 (userNo)
- 用户姓名 (userName)
- 账单ID (billId) - 必填
- 账单编号 (billNumber)
- 原违约金 (originalPenalty) - 必填，数字输入，带单位
- 调整后违约金 (adjustedPenalty) - 必填，数字输入，带单位
- 调整金额 (adjustmentAmount) - 自动计算，只读
- 调整类型 (adjustmentType) - 字典选择
- 调整原因 (reason) - 必填
- 调整时间 (adjustmentTime) - 日期时间选择
- 操作员 (operator)
- 备注 (remark) - 文本域

### 5. 应收账分账 (chargeManageArAdjustment)
**文件**: `apps/web-antd/src/views/waterfee/chargeManageArAdjustment/data.tsx`

#### 查询表单新增字段
- 用户编号 (userNo)
- 水表编号 (meterNo)
- 账期 (billPeriod)
- 原应收金额数字输入 (originalAmount)
- 调整后金额数字输入 (adjustedAmount)
- 调整类型选择器 (adjustmentType)

#### 抽屉表单新增字段
- 用户ID (userId) - 必填
- 用户编号 (userNo)
- 用户姓名 (userName)
- 水表编号 (meterNo) - 必填
- 账期 (billPeriod) - 必填
- 账单编号 (billNumber)
- 原应收金额 (originalAmount) - 必填，数字输入，带单位
- 调整后金额 (adjustedAmount) - 必填，数字输入，带单位
- 调整金额 (adjustmentAmount) - 自动计算，只读
- 调整类型 (adjustmentType) - 必填，字典选择
- 调整原因 (reason) - 必填
- 调整时间 (adjustmentTime) - 日期时间选择
- 操作员 (operator)
- 备注 (remark) - 文本域

### 6. 第三方对账记录 (chargeManageThirdParty)
**文件**: `apps/web-antd/src/views/waterfee/chargeManageThirdParty/data.tsx`

#### 查询表单新增字段
- 渠道编码选择器 (channelCode)
- 本地订单号 (localOrderNo)
- 第三方订单号 (thirdOrderNo)
- 交易金额数字输入 (amount)
- 对账状态选择器 (status)

#### 抽屉表单新增字段
- 渠道编码 (channelCode) - 必填，字典选择
- 渠道名称 (channelName)
- 本地订单号 (localOrderNo) - 必填
- 第三方订单号 (thirdOrderNo) - 必填
- 交易金额 (amount) - 必填，数字输入，带单位
- 本地金额 (localAmount) - 数字输入，带单位
- 第三方金额 (thirdAmount) - 数字输入，带单位
- 对账状态 (status) - 必填，字典选择
- 对账日期 (reconciliationDate) - 必填，日期时间选择
- 交易时间 (transactionTime) - 日期时间选择
- 操作员 (operator)
- 备注 (remark) - 文本域

### 7. 用户关系维护 (chargeManageUserRelation)
**文件**: `apps/web-antd/src/views/waterfee/chargeManageUserRelation/data.tsx`

#### 查询表单新增字段
- 主用户编号 (masterUserNo)
- 附属用户编号 (attachedUserNo)
- 关系类型选择器 (relationType)

#### 抽屉表单新增字段
- 主用户ID (masterUserId) - 必填
- 主用户编号 (masterUserNo)
- 主用户姓名 (masterUserName)
- 附属用户ID (attachedUserId) - 必填
- 附属用户编号 (attachedUserNo)
- 附属用户姓名 (attachedUserName)
- 关系类型 (relationType) - 必填，字典选择
- 关系开始日期 (relationStartDate) - 日期选择
- 关系结束日期 (relationEndDate) - 日期选择
- 关系状态 (status) - 字典选择
- 操作员 (operator)
- 备注 (remark) - 文本域

## 改进特点

### 1. 统一的用户体验
- 所有输入框都添加了placeholder提示
- 数字输入框统一使用InputNumber组件
- 日期选择器统一格式和样式
- 文本域统一字符限制和计数显示

### 2. 完善的数据验证
- 必填字段添加required规则
- 数字输入添加最小值和精度限制
- 金额字段统一添加"元"单位后缀
- 文本域添加最大长度限制

### 3. 智能的字段关联
- 代扣配置中签约时间和取消时间根据签约状态条件显示
- 调整金额字段设为只读，由系统自动计算
- 字典选择器统一使用getDictOptions

### 4. 丰富的业务字段
- 添加用户编号、用户姓名等关联信息
- 增加操作员字段用于审计追踪
- 补充时间字段记录业务发生时间
- 扩展状态字段支持业务流程管理

### 5. 优化的查询功能
- 查询表单添加更多筛选条件
- 支持模糊查询和精确查询
- 添加allowClear属性支持快速清空

## 技术规范

### 组件使用规范
- Input: 文本输入
- InputNumber: 数字输入，带精度控制
- Select: 下拉选择，使用字典数据
- DatePicker: 日期时间选择
- Textarea: 多行文本输入

### 字段命名规范
- ID字段: xxxId
- 编号字段: xxxNo  
- 名称字段: xxxName
- 时间字段: xxxTime/xxxDate
- 金额字段: xxxAmount
- 状态字段: status

### 验证规则规范
- 必填: rules: 'required'
- 数字: min, precision属性
- 文本: maxlength属性
- 日期: valueFormat格式化

这些改进大大提升了表单的完整性和用户体验，使得数据录入更加规范和高效。
