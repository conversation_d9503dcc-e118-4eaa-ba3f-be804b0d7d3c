import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'billId',
    label: '账单ID',
  },
  {
    component: 'Input',
    fieldName: 'projectCode',
    label: '项目编码',
  },
  {
    component: 'Input',
    fieldName: 'departmentId',
    label: '部门ID',
  },
  {
    component: 'Input',
    fieldName: 'splitAmount',
    label: '分账金额',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键',
    field: 'id',
  },
  {
    title: '账单ID',
    field: 'billId',
  },
  {
    title: '项目编码',
    field: 'projectCode',
  },
  {
    title: '部门ID',
    field: 'departmentId',
  },
  {
    title: '分账金额',
    field: 'splitAmount',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'billId',
    label: '账单ID',
rules: 'required',  },
  {
    component: 'Input',
    fieldName: 'projectCode',
    label: '项目编码',
  },
  {
    component: 'Input',
    fieldName: 'departmentId',
    label: '部门ID',
  },
  {
    component: 'Input',
    fieldName: 'splitAmount',
    label: '分账金额',
rules: 'required',  },
  {
    component: 'Input',
    fieldName: 'remark',
    label: '备注',
  },
];
