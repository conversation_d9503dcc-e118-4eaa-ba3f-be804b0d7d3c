import type { UserVO } from '../archivesManage/model';

export interface ConsolidatedAccountVO {
  /**
   * 主键
   */
  consolidatedAccountId: number | string;

  /**
   * 合收户编号
   */
  consolidatedAccountNo: number | string;

  /**
   * 合收户名
   */
  consolidatedAccountName: number | string;

  /**
   * 户主姓名
   */
  ownerName: string;

  /**
   * 户主电话
   */
  ownerPhone: string;

  /**
   * 备注
   */
  remark: string;
}

export interface ConsolidatedAccountForm extends BaseEntity {
  /**
   * 主键
   */
  consolidatedAccountId?: number | string;

  /**
   * 合收户编号
   */
  consolidatedAccountNo?: number | string;

  /**
   * 合收户名
   */
  consolidatedAccountName?: number | string;

  /**
   * 户主姓名
   */
  ownerName?: string;

  /**
   * 户主电话
   */
  ownerPhone?: string;

  /**
   * 备注
   */
  remark?: string;
  userList: UserVO[];
}

export interface ConsolidatedAccountQuery extends PageQuery {
  /**
   * 合收户编号
   */
  consolidatedAccountNo?: number | string;

  /**
   * 合收户名
   */
  consolidatedAccountName?: number | string;

  /**
   * 户主姓名
   */
  ownerName?: string;

  /**
   * 户主电话
   */
  ownerPhone?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
