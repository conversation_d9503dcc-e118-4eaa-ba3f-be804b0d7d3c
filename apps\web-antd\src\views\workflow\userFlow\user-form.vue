<script setup lang="ts">
import type { Area } from '#/api/waterfee/area/model';
import type { UserFlowStepsInfo } from '#/api/waterfee/user/archivesManage/model';
import type { StartWorkFlowReqData } from '#/api/workflow/task/model';

import { computed, onMounted, ref, useTemplateRef } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';
import { addFullName, listToTree } from '@vben/utils';

import { Card } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { useVbenForm } from '#/adapter/form';
import { areaNodeList, areaOptions } from '#/api/waterfee/area/index';
import {
  addUser,
  getUserFlowDetail,
  updateUser,
} from '#/api/waterfee/user/archivesManage/index';
import { startWorkFlow } from '#/api/workflow/task';

import { applyModal } from '../components';
import { flowSchema } from './data';
import UserDescription from './user-description.vue';

const route = useRoute();
const readonly = route.query?.readonly === 'true';
const id = route.query?.id as string;

/**
 * id存在&readonly时候
 */
const showActionBtn = computed(() => {
  return !readonly;
});

/**
 * 获取区域树状数据
 * @param areaId 区域ID
 * @param exclude 是否排除当前区域
 */
async function getAreaTree(areaId?: number | string, exclude = false) {
  let ret: Area[] = [];
  ret = await (!areaId || exclude ? areaOptions({}) : areaNodeList(areaId));
  const treeData = listToTree(ret, { id: 'areaId', pid: 'parentId' });
  // 添加营业区域名称 如 xx-xx-xx
  addFullName(treeData, 'areaName', ' / ');

  // 递归设置节点的可选状态
  const setSelectable = (nodes: any[]) => {
    nodes.forEach((node) => {
      // 如果有子节点，则设置为不可选
      node.selectable = !node.children?.length;
      if (node.children?.length) {
        setSelectable(node.children);
      }
    });
  };

  setSelectable(treeData);
  return treeData;
}

async function loadAreaOptions() {
  try {
    const areaData = await getAreaTree();

    formApi.updateSchema([
      {
        componentProps: {
          treeData: areaData,
        },
        fieldName: 'areaId',
      },
    ]);
  } catch (error) {
    console.error('Error loading area:', error);
  }
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-1',
    // 默认label宽度 px
    labelWidth: 100,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
      disabled: readonly,
    },
  },
  schema: flowSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const userDescription = ref<UserFlowStepsInfo>();
const showDescription = computed(() => {
  return readonly && userDescription.value;
});
const cardRef = useTemplateRef('cardRef');
onMounted(async () => {
  // 加载区域
  await loadAreaOptions();
  // 只读 获取信息赋值
  if (id) {
    const { userBasicInfo, userPrice, waterfeeMeterBo } =
      await getUserFlowDetail(id);
    userDescription.value = { userBasicInfo, userPrice, waterfeeMeterBo };

    await formApi.setValues(userBasicInfo);

    /**
     * window.parent（最近的上一级父页面）
     * 主要解决内嵌iframe卡顿的问题
     */
    if (readonly) {
      // 渲染完毕才显示表单
      window.parent.postMessage({ type: 'mounted' }, '*');
      // 获取表单高度 内嵌时保持一致
      setTimeout(() => {
        const el = cardRef.value?.$el as HTMLDivElement;
        // 获取高度
        const height = el?.offsetHeight ?? 0;
        if (height) {
          window.parent.postMessage({ type: 'height', height }, '*');
        }
      });
    }
  }
});

const router = useRouter();

/**
 * 提取通用逻辑
 */
async function handleSaveOrUpdate() {
  const { valid } = await formApi.validate();
  if (!valid) {
    return;
  }
  const data = cloneDeep(await formApi.getValues()) as any;
  if (id) {
    data.userId = id;
    return await updateUser(data);
  } else {
    return await addUser(data);
  }
}

const [ApplyModal, applyModalApi] = useVbenModal({
  connectedComponent: applyModal,
});
/**
 * 暂存 草稿状态
 */
async function handleTempSave() {
  try {
    const r = await handleSaveOrUpdate();
    if (r === undefined) {
      return;
    }
    router.push('/businessCharges/fileManagement/user-flow');
  } catch (error) {
    console.error(error);
  }
}

/**
 * 保存业务 & 发起流程
 */
async function handleStartWorkFlow() {
  try {
    // 保存业务
    const resp = await handleSaveOrUpdate();
    // 校验不通过
    if (resp === undefined) {
      return;
    }
    // 编辑状态只保存信息，不发起流程
    // if (id) {
    //   router.push('/register/user-flow');
    //   return;
    // }
    // 启动流程
    const startWorkFlowData: StartWorkFlowReqData = {
      businessId: (resp as any)?.userId,
      flowCode: 'newWaterfeeUser',
      // variables: taskVariables,
      variables: {},
    };
    const { taskId } = await startWorkFlow(startWorkFlowData);
    // 打开窗口
    applyModalApi.setData({
      taskId,
      // taskVariables,
      variables: {},
    });
    applyModalApi.open();
  } catch (error) {
    console.error(error);
  }
}

function handleComplete() {
  formApi.resetForm();
  router.push('/businessCharges/fileManagement/user-flow');
}

/**
 * 显示详情时 需要较小的padding
 */
const cardSize = computed(() => {
  return showDescription.value ? 'small' : 'default';
});
</script>

<template>
  <Card ref="cardRef" :size="cardSize">
    <div id="leave-form">
      <!-- 使用v-if会影响生命周期 -->
      <BasicForm v-show="!showDescription" />
      <UserDescription v-if="showDescription" :data="userDescription!" />
      <div v-if="showActionBtn" class="flex justify-end gap-2">
        <a-button @click="handleTempSave">暂存</a-button>
        <a-button type="primary" @click="handleStartWorkFlow">提交</a-button>
      </div>
      <ApplyModal @complete="handleComplete" />
    </div>
  </Card>
</template>

<style lang="scss">
html:has(#leave-form) {
  /**
  去除顶部进度条样式
  */
  #nprogress {
    display: none;
  }
}
</style>
