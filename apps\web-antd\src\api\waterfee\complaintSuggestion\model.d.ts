export interface ComplaintSuggestionVO {
  /**
   * ID
   */
  complaintSuggestionId: number | string;

  /**
   * 提交人姓名
   */
  submitterName: string;

  /**
   * 联系电话
   */
  contactPhone: string;

  /**
   * 投诉内容
   */
  content: string;

  /**
   * 投诉时间
   */
  submitTime: string;

  /**
   * 备注
   */
  remark: string;
}

export interface ComplaintSuggestionForm extends BaseEntity {
  /**
   * ID
   */
  complaintSuggestionId?: number | string;

  /**
   * 提交人姓名
   */
  submitterName?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 投诉内容
   */
  content?: string;

  /**
   * 投诉时间
   */
  submitTime?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface ComplaintSuggestionQuery extends PageQuery {
  /**
   * 提交人姓名
   */
  submitterName?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 投诉内容
   */
  content?: string;

  /**
   * 投诉时间
   */
  submitTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
