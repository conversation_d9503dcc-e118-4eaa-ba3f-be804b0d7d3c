import type { ChargeManageUserRelationVO, ChargeManageUserRelationForm, ChargeManageUserRelationQuery } from '#/api/waterfee/chargeManageUserRelation/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/chargeManageUserRelation',
  list = '/waterfee/chargeManageUserRelation/list'
}

/**
 * 依托用户关系维护导出
 * @param data data
 * @returns void
 */
export function ChargeManageUserRelationExport(data: Partial<ChargeManageUserRelationForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询依托用户关系维护列表
 * @param params 查询参数
 * @returns 依托用户关系维护列表
 */
export function listChargeManageUserRelation(params?: ChargeManageUserRelationQuery & PageQuery) {
  return requestClient.get<ChargeManageUserRelationVO>(Api.list, { params });
}

/**
 * 查询依托用户关系维护详细
 * @param id 依托用户关系维护ID
 * @returns 依托用户关系维护信息
 */
export function getChargeManageUserRelation(id: string | number) {
  return requestClient.get<ChargeManageUserRelationForm>(`${Api.root}/${id}`);
}

/**
 * 新增依托用户关系维护
 * @param data 新增数据
 * @returns void
 */
export function addChargeManageUserRelation(data: ChargeManageUserRelationForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改依托用户关系维护
 * @param data 修改数据
 * @returns void
 */
export function updateChargeManageUserRelation(data: ChargeManageUserRelationForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除依托用户关系维护
 * @param id 依托用户关系维护ID或ID数组
 * @returns void
 */
export function delChargeManageUserRelation(id: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
