import type {
  MeterListGetResultModel,
  MeterModel,
  MeterParams,
} from './model/meter/meterModel';

import type { ID } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

import { ensureMeterIdString, preserveIdPrecision } from './meter/index';

enum Api {
  associate = '/waterfee/meter/associate',
  byId = '/waterfee/meter/id',
  byIds = '/waterfee/meter/ids',
  byNo = '/waterfee/meter/no',
  export = '/waterfee/meter/export',
  importData = '/waterfee/meter/importData',
  importTemplate = '/waterfee/meter/importTemplate',
  meterList = '/waterfee/meter/list',
  root = '/waterfee/meter',
}

/**
 * 水表列表
 * @param params 查询参数
 * @returns 水表列表
 */
export function meterList(params?: MeterParams) {
  // 处理日期范围
  const queryParams = { ...params };
  if (
    queryParams.installDateRange &&
    Array.isArray(queryParams.installDateRange)
  ) {
    queryParams.installDateStart = queryParams.installDateRange[0];
    queryParams.installDateEnd = queryParams.installDateRange[1];
    delete queryParams.installDateRange; // 删除前端专用字段
  }

  // 处理查询参数中可能包含的ID
  if (queryParams.businessAreaId) {
    queryParams.businessAreaId = ensureMeterIdString(
      queryParams.businessAreaId,
    );
  }

  if (queryParams.meterBookId) {
    queryParams.meterBookId = ensureMeterIdString(queryParams.meterBookId);
  }

  // 使用JSON序列化和反序列化保留精度
  const safeParams = preserveIdPrecision(queryParams);

  return requestClient.get<MeterListGetResultModel>(Api.meterList, {
    params: safeParams,
  });
}

/**
 * 水表详情（根据ID）
 * @param id 水表ID
 * @returns 水表详情
 */
export function meterInfoById(id: ID) {
  // 确保ID作为字符串处理，并使用preserveIdPrecision保留精度
  const safeId = ensureMeterIdString(id);

  // 使用模板字符串而不是直接拼接，避免可能的类型转换
  return requestClient.get<MeterModel>(`${Api.byId}/${safeId}`);
}

/**
 * 批量获取水表详情（根据多个ID）
 * @param ids 水表ID数组
 * @returns 水表详情列表
 */
export function meterInfoByIds(ids: ID[]) {
  // 转为字符串数组，确保 ID 精度（如果是大数）
  const safeIds = ids.map((id) => ensureMeterIdString(id));

  // 使用 POST 请求传数组，避免 URL 太长
  return requestClient.post<MeterModel[]>(`${Api.byIds}`, safeIds);
}

/**
 * 批量获取水表信息详细信息（根据水表编号）
 * @param meterNos 水表编号数组
 * @returns 水表详情列表
 */
export function meterInfoByNos(meterNos: string[]) {
  // 使用 POST 请求传数组，避免 URL 太长
  return requestClient.post<MeterModel[]>('/waterfee/meter/nos', meterNos);
}

/**
 * 水表详情（根据编号）
 * @param meterNo 水表编号
 * @returns 水表详情
 */
export function meterInfoByNo(meterNo: string) {
  return requestClient.get<MeterModel>(`${Api.byNo}/${meterNo}`);
}

/**
 * 水表新增
 * @param data 参数
 */
export function meterAdd(data: Partial<MeterModel>) {
  // 使用preserveIdPrecision确保大整数ID不会丢失精度
  const safeData = preserveIdPrecision(data);

  // 额外确保关键ID字段是字符串
  if (safeData.meterId)
    safeData.meterId = ensureMeterIdString(safeData.meterId);
  if (safeData.userId) safeData.userId = ensureMeterIdString(safeData.userId);
  if (safeData.businessAreaId)
    safeData.businessAreaId = ensureMeterIdString(safeData.businessAreaId);
  if (safeData.meterBookId)
    safeData.meterBookId = ensureMeterIdString(safeData.meterBookId);

  return requestClient.postWithMsg<void>(Api.root, safeData);
}

/**
 * 水表更新
 * @param data 参数
 */
export function meterUpdate(data: Partial<MeterModel>) {
  // 使用preserveIdPrecision确保大整数ID不会丢失精度
  const safeData = preserveIdPrecision(data);

  // 额外确保关键ID字段是字符串
  if (safeData.meterId)
    safeData.meterId = ensureMeterIdString(safeData.meterId);
  if (safeData.userId) safeData.userId = ensureMeterIdString(safeData.userId);
  if (safeData.businessAreaId)
    safeData.businessAreaId = ensureMeterIdString(safeData.businessAreaId);
  if (safeData.meterBookId)
    safeData.meterBookId = ensureMeterIdString(safeData.meterBookId);

  return requestClient.putWithMsg<void>(Api.root, safeData);
}

/**
 * 水表删除
 * @param id 水表ID
 * @returns void
 */
export function meterRemove(id: ID | ID[]) {
  // 确保ID作为字符串处理
  const ids = Array.isArray(id)
    ? id.map((item) => ensureMeterIdString(item)).join(',')
    : ensureMeterIdString(id);

  return requestClient.deleteWithMsg<void>(`${Api.root}/${ids}`);
}

/**
 * 关联用水户
 * @param meterId 水表ID
 * @param userId 用水户ID
 * @returns void
 */
export function meterAssociateUser(meterId: ID, userId: ID) {
  const safeMeterId = ensureMeterIdString(meterId);
  const safeUserId = ensureMeterIdString(userId);

  return requestClient.putWithMsg<void>(
    `${Api.associate}/${safeMeterId}`,
    null,
    {
      params: { userId: safeUserId },
    },
  );
}

/**
 * 导出水表数据
 * @param data 查询参数
 * @returns blob
 */
export function meterExport(data: Partial<MeterParams>) {
  return commonExport(Api.export, data);
}

/**
 * 下载导入模板
 * @param meterType 水表类型 1-机械表 2-智能表
 * @returns blob
 */
export function meterImportTemplate(meterType: number) {
  return requestClient.get<Blob>(`/waterfee/meter/importTemplate`, {
    params: { meterType },
    responseType: 'blob',
    isTransformResponse: false,
  });
}

/**
 * 导入水表数据
 * @param data 表单数据，包含文件和水表类型
 * @returns 导入结果
 */
export function meterImportData(data: FormData) {
  return requestClient.post<{ code: number; msg: string }>(
    Api.importData,
    data,
    {
      // headers: {
      //   'Content-Type': ContentTypeEnum.FORM_DATA,
      // },
      isTransformResponse: false,
    },
  );
}
